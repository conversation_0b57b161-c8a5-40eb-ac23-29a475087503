// This script applies the SQL migrations to the Supabase database
// Run with: node scripts/apply-migrations.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or service role key. Please check your .env.local file.');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applyMigration(filePath) {
  try {
    const sql = fs.readFileSync(filePath, 'utf8');
    console.log(`Applying migration: ${path.basename(filePath)}`);
    
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error(`Error applying migration ${path.basename(filePath)}:`, error);
      return false;
    }
    
    console.log(`Successfully applied migration: ${path.basename(filePath)}`);
    return true;
  } catch (err) {
    console.error(`Error reading or applying migration ${path.basename(filePath)}:`, err);
    return false;
  }
}

async function main() {
  const migrationsDir = path.resolve(__dirname, '../supabase/migrations');
  
  try {
    // Check if migrations directory exists
    if (!fs.existsSync(migrationsDir)) {
      console.error(`Migrations directory not found: ${migrationsDir}`);
      process.exit(1);
    }
    
    // Get all SQL files in the migrations directory
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Sort to apply in order
    
    if (migrationFiles.length === 0) {
      console.log('No migration files found.');
      process.exit(0);
    }
    
    console.log(`Found ${migrationFiles.length} migration files.`);
    
    // Apply each migration
    let successCount = 0;
    for (const file of migrationFiles) {
      const filePath = path.join(migrationsDir, file);
      const success = await applyMigration(filePath);
      if (success) {
        successCount++;
      }
    }
    
    console.log(`Applied ${successCount} of ${migrationFiles.length} migrations.`);
    
    if (successCount < migrationFiles.length) {
      process.exit(1);
    }
  } catch (err) {
    console.error('Error applying migrations:', err);
    process.exit(1);
  }
}

// Create the exec_sql function if it doesn't exist
async function createExecSqlFunction() {
  const { error } = await supabase.rpc('exec_sql', { 
    sql: `
      CREATE OR REPLACE FUNCTION exec_sql(sql text) RETURNS void AS $$
      BEGIN
        EXECUTE sql;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `
  }).catch(() => {
    // If the function doesn't exist yet, create it directly
    return supabase.sql(`
      CREATE OR REPLACE FUNCTION exec_sql(sql text) RETURNS void AS $$
      BEGIN
        EXECUTE sql;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `);
  });
  
  if (error) {
    console.error('Error creating exec_sql function:', error);
    process.exit(1);
  }
  
  console.log('Successfully created or verified exec_sql function.');
}

// Run the script
(async () => {
  try {
    await createExecSqlFunction();
    await main();
  } catch (err) {
    console.error('Unhandled error:', err);
    process.exit(1);
  }
})();
