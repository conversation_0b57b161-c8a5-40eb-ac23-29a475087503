// This script applies the database schema to Supabase cloud
// Run with: node scripts/apply-schema-to-cloud.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or service role key. Please check your .env.local file.');
  console.error('Required environment variables:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applySqlFile(filePath) {
  try {
    const sql = fs.readFileSync(filePath, 'utf8');
    console.log(`Applying schema: ${path.basename(filePath)}`);
    
    // Split SQL into individual statements
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`Executing: ${statement.substring(0, 50)}...`);
        const { error } = await supabase.rpc('exec_sql', { sql: statement + ';' });
        
        if (error) {
          console.error(`Error executing statement: ${error.message}`);
          console.error(`Statement: ${statement}`);
          // Continue with other statements
        } else {
          console.log('✓ Statement executed successfully');
        }
      }
    }
    
    console.log(`✓ Completed applying ${path.basename(filePath)}`);
  } catch (error) {
    console.error(`Error applying ${path.basename(filePath)}:`, error.message);
  }
}

async function createExecSqlFunction() {
  console.log('Creating exec_sql function...');
  
  const createFunctionSql = `
    CREATE OR REPLACE FUNCTION exec_sql(sql text) RETURNS void AS $$
    BEGIN
      EXECUTE sql;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;
  `;
  
  try {
    const { error } = await supabase.rpc('exec_sql', { sql: createFunctionSql });
    if (error) {
      // Try direct SQL execution
      const { error: directError } = await supabase.from('_').select('*').limit(0);
      if (directError) {
        console.log('Creating function using direct SQL...');
        // This is a workaround - in practice, you'd need to run this in Supabase SQL editor
        console.log('Please run the following SQL in your Supabase SQL editor:');
        console.log(createFunctionSql);
        return false;
      }
    }
    console.log('✓ exec_sql function created successfully');
    return true;
  } catch (error) {
    console.error('Error creating exec_sql function:', error.message);
    console.log('Please run the following SQL in your Supabase SQL editor:');
    console.log(createFunctionSql);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting database schema application...');
  console.log(`Supabase URL: ${supabaseUrl}`);
  
  // Create exec_sql function first
  const functionCreated = await createExecSqlFunction();
  
  if (!functionCreated) {
    console.log('\n⚠️  Please create the exec_sql function manually in Supabase SQL editor first.');
    console.log('Then run this script again.');
    return;
  }
  
  // Apply the main schema
  const schemaPath = path.resolve(__dirname, '../supabase/migrations/finder_schema.sql');
  
  if (fs.existsSync(schemaPath)) {
    await applySqlFile(schemaPath);
  } else {
    console.error(`Schema file not found: ${schemaPath}`);
    return;
  }
  
  // Apply additional migrations
  const migrationsDir = path.resolve(__dirname, '../supabase/migrations');
  const migrationFiles = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql') && file !== 'finder_schema.sql')
    .sort();
  
  for (const file of migrationFiles) {
    const filePath = path.join(migrationsDir, file);
    await applySqlFile(filePath);
  }
  
  console.log('\n✅ Database schema application completed!');
  console.log('\nNext steps:');
  console.log('1. Check your Supabase dashboard to verify tables were created');
  console.log('2. Test the admin panel at http://localhost:3000/admin');
  console.log('3. Create some test data if needed');
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('Unhandled rejection:', error);
  process.exit(1);
});

main().catch(console.error);
