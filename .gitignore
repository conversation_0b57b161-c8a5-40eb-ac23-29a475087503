# Dependencies
node_modules/
.pnp/
.pnp.js

# Testing
coverage/

# Next.js
.next/
out/
build/
dist/

# Supabase
.supabase/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Local Netlify folder
.netlify

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE and editors
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.pem

# Cache
.eslintcache
.stylelintcache

# Misc
.DS_Store
*.pem

*.sql

*.md