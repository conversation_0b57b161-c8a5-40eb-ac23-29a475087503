'use client';

import React, { useState, useEffect } from 'react';
import { Star, Calendar, MessageSquare } from 'lucide-react';
import { createSPASassClient } from '@/lib/supabase/client';
import StoreReviewForm from './StoreReviewForm';

interface Review {
  id: string;
  user_id: string;
  rating: number;
  title?: string;
  content?: string;
  created_at: string;
  profiles?: {
    first_name?: string;
    last_name?: string;
  };
}

interface StoreReviewsProps {
  storeId: string;
  storeName: string;
}

export default function StoreReviews({ storeId, storeName }: StoreReviewsProps) {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAll, setShowAll] = useState(false);

  useEffect(() => {
    loadReviews();
  }, [storeId]);

  const loadReviews = async () => {
    setLoading(true);
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data, error } = await supabase
        .from('reviews')
        .select(`
          *,
          profiles(first_name, last_name)
        `)
        .eq('store_id', storeId)
        .order('created_at', { ascending: false })
        .limit(showAll ? 50 : 5);

      if (error) throw error;
      setReviews(data || []);
    } catch (error) {
      console.error('Error loading reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating: number, size = 'w-4 h-4') => (
    <div className="flex gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`${size} ${
            star <= rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'
          }`}
        />
      ))}
    </div>
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDisplayName = (review: Review) => {
    if (review.profiles?.first_name || review.profiles?.last_name) {
      return `${review.profiles.first_name || ''} ${review.profiles.last_name || ''}`.trim();
    }
    return 'Customer';
  };

  const getInitials = (review: Review) => {
    if (review.profiles?.first_name || review.profiles?.last_name) {
      const first = review.profiles.first_name?.charAt(0)?.toUpperCase() || '';
      const last = review.profiles.last_name?.charAt(0)?.toUpperCase() || '';
      return first + last || 'C';
    }
    
    // Generate consistent initials from user ID as fallback
    const hash = review.user_id.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const first = letters[Math.abs(hash) % 26];
    const second = letters[Math.abs(hash >> 8) % 26];
    return first + second;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Customer Reviews</h3>
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="border rounded-lg p-4">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-24 mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
              <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (reviews.length === 0) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Customer Reviews</h3>
        <div className="text-center py-8 text-gray-500">
          <MessageSquare className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>No reviews yet</p>
          <p className="text-sm">Be the first to review {storeName}!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Customer Reviews ({reviews.length})</h3>
        {reviews.length > 5 && !showAll && (
          <button
            onClick={() => {
              setShowAll(true);
              loadReviews();
            }}
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            View All Reviews
          </button>
        )}
      </div>

      {/* Review Form */}
      <StoreReviewForm 
        storeId={storeId} 
        storeName={storeName}
        onReviewSubmitted={loadReviews}
      />

      <div className="space-y-4">
        {reviews.map((review) => (
          <div key={review.id} className="border rounded-lg p-4 hover:shadow-sm transition-shadow">
            {/* Header */}
            <div className="flex items-start gap-3 mb-3">
              <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-primary-700">
                  {getInitials(review)}
                </span>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium text-gray-900">
                    {getDisplayName(review)}
                  </span>
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    <Calendar className="w-3 h-3" />
                    {formatDate(review.created_at)}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {renderStars(review.rating)}
                  <span className="text-sm text-gray-600">({review.rating}/5)</span>
                </div>
              </div>
            </div>

            {/* Review Title */}
            {review.title && (
              <h4 className="font-medium text-gray-900 mb-2">{review.title}</h4>
            )}

            {/* Review Content */}
            {review.content && (
              <p className="text-gray-700 leading-relaxed">{review.content}</p>
            )}
          </div>
        ))}
      </div>

      {showAll && reviews.length > 5 && (
        <div className="text-center">
          <button
            onClick={() => {
              setShowAll(false);
              loadReviews();
            }}
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            Show Less
          </button>
        </div>
      )}
    </div>
  );
}