'use client';

import React, { useState } from 'react';
import { Star, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { createSPASassClient } from '@/lib/supabase/client';

interface StoreReviewFormProps {
  storeId: string;
  storeName: string;
  onReviewSubmitted?: () => void;
}

export default function StoreReviewForm({ storeId, storeName, onReviewSubmitted }: StoreReviewFormProps) {
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showForm, setShowForm] = useState(false);

  React.useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();
      const { data: { user } } = await supabase.auth.getUser();
      setIsAuthenticated(!!user);
    } catch (error) {
      console.error('Error checking auth:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (rating === 0) {
      alert('Please provide a rating');
      return;
    }

    setIsSubmitting(true);
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { error } = await supabase
        .from('reviews')
        .insert({
          store_id: storeId,
          product_id: null, // Store-only review
          rating,
          title: title.trim() || null,
          content: content.trim() || null
        });

      if (error) throw error;

      // Reset form
      setRating(0);
      setTitle('');
      setContent('');
      setShowForm(false);
      
      // Notify parent component
      onReviewSubmitted?.();
      
      alert('Review submitted successfully!');
    } catch (error: any) {
      console.error('Error submitting review:', error);
      if (error.code === '23505') {
        alert('You have already reviewed this store.');
      } else {
        alert('Failed to submit review. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStars = (interactive = false) => (
    <div className="flex gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <button
          key={star}
          type="button"
          disabled={!interactive}
          onClick={() => interactive && setRating(star)}
          onMouseEnter={() => interactive && setHoveredRating(star)}
          onMouseLeave={() => interactive && setHoveredRating(0)}
          className={`${interactive ? 'hover:scale-110 transition-transform cursor-pointer' : 'cursor-default'}`}
        >
          <Star
            className={`w-6 h-6 ${
              star <= (interactive ? (hoveredRating || rating) : rating)
                ? 'text-yellow-400 fill-yellow-400'
                : 'text-gray-300'
            }`}
          />
        </button>
      ))}
    </div>
  );

  if (!isAuthenticated) {
    return (
      <div className="bg-gray-50 rounded-lg p-6 text-center">
        <p className="text-gray-600 mb-4">Please sign in to leave a review for {storeName}</p>
        <Button 
          onClick={() => window.location.href = '/auth/login'}
          variant="outline"
        >
          Sign In
        </Button>
      </div>
    );
  }

  if (!showForm) {
    return (
      <div className="bg-gray-50 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900">Share Your Experience</h4>
            <p className="text-sm text-gray-600">Help others by reviewing {storeName}</p>
          </div>
          <Button onClick={() => setShowForm(true)}>
            Write Review
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 rounded-lg p-6">
      <h4 className="font-medium text-gray-900 mb-4">Write a Review for {storeName}</h4>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Rating */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Your Rating *
          </label>
          {renderStars(true)}
        </div>

        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Review Title (optional)
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="Summarize your experience"
            maxLength={100}
          />
        </div>

        {/* Content */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Your Review (optional)
          </label>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="Tell others about your experience with this store..."
            maxLength={500}
          />
          <p className="text-xs text-gray-500 mt-1">
            {content.length}/500 characters
          </p>
        </div>

        {/* Submit Buttons */}
        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowForm(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || rating === 0}
            className="flex items-center gap-2"
          >
            {isSubmitting ? (
              'Submitting...'
            ) : (
              <>
                <Send className="w-4 h-4" />
                Submit Review
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}