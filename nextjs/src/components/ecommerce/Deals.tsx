"use client";

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRight, Clock } from 'lucide-react';
import { Product } from '@/lib/types/ecommerce';
import { DealsService, DealProduct } from '@/lib/services/deals';
import { formatCurrency, calculateDiscountPercentage } from '@/lib/utils/format';

interface DealsProps {
  title?: string;
  subtitle?: string;
  viewAllLink?: string;
  initialProducts?: Product[];
  limit?: number;
}

export default function Deals({
  title = "Limited Time Deals",
  subtitle = "Hurry up! These deals won't last long.",
  viewAllLink = "/deals",
  initialProducts,
  limit = 3
}: DealsProps) {
  const [products, setProducts] = useState<DealProduct[]>([]);
  const [loading, setLoading] = useState(!initialProducts);

  useEffect(() => {
    if (!initialProducts) {
      const fetchProducts = async () => {
        try {
          // Use DealsService to get today's best deals
          const dealProducts = await DealsService.getTodaysDeals(limit);
          setProducts(dealProducts);
        } catch (error) {
          console.error('Error fetching deals:', error);
        } finally {
          setLoading(false);
        }
      };

      fetchProducts();
    } else {
      // Transform initial products to deal products if provided
      const dealProducts = initialProducts
        .filter(product => DealsService.isOnSale(product))
        .map(product => {
          const originalPrice = product.compare_at_price || product.price;
          const discountPercentage = Math.round(((originalPrice - product.price) / originalPrice) * 100);
          return {
            ...product,
            discountPercentage,
            discountAmount: originalPrice - product.price,
            dealType: 'regular' as const
          };
        })
        .slice(0, limit);
      setProducts(dealProducts);
    }
  }, [initialProducts, limit]);

  // Calculate remaining time (just for display purposes)
  const getRandomHours = () => Math.floor(Math.random() * 24) + 1;
  const getRandomMinutes = () => Math.floor(Math.random() * 60);

  return (
    <section className="bg-gradient-to-r from-red-50 to-pink-50 py-12 sm:py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col md:flex-row md:items-start md:justify-between mb-8">
          <div className="flex-1 text-center md:text-left">
            <h2 className="text-2xl font-bold tracking-tight text-gray-900 sm:text-3xl">
              {title}
            </h2>
            <p className="mt-3 max-w-2xl text-lg text-gray-600 md:mx-0 mx-auto">
              {subtitle}
            </p>
          </div>
          {viewAllLink && (
            <div className="mt-4 md:mt-0 md:ml-6 flex-shrink-0 text-center md:text-right">
              <Link
                href={viewAllLink}
                className="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium"
              >
                View all deals
                <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
          )}
        </div>

        <div className="mt-10">
          {loading ? (
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {Array.from({ length: limit }).map((_, index) => (
                <div key={index} className="h-96 animate-pulse rounded-lg bg-gray-200"></div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {products.map((product) => {
                const badge = DealsService.getDealBadge(product.discountPercentage);

                return (
                  <Link
                    key={product.id}
                    href={`/products/${product.slug || product.id}`}
                    className="group overflow-hidden rounded-lg bg-white shadow-md transition-all hover:shadow-lg"
                  >
                    <div className="relative h-64 overflow-hidden">
                      {(product.image_url || product.imageUrl || (product.images && product.images[0]?.url)) ? (
                        <Image
                          src={product.image_url || product.imageUrl || product.images?.[0]?.url || ''}
                          alt={product.name}
                          fill
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          className="object-cover object-center transition-transform duration-300 group-hover:scale-105"
                        />
                      ) : (
                        <div className="flex h-full items-center justify-center bg-gray-200">
                          <span className="text-gray-400">No image</span>
                        </div>
                      )}

                      {/* Discount badge */}
                      <div className={`absolute left-3 top-3 rounded-full px-3 py-1.5 text-sm font-bold ${badge.color} ${badge.bgColor}`}>
                        {product.discountPercentage}% OFF
                      </div>
                    </div>

                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-900">{product.name}</h3>

                      <div className="mt-2 flex items-baseline">
                        <span className="text-2xl font-bold text-primary-600">
                          {formatCurrency(product.price, product.currency || 'GMD')}
                        </span>
                        {product.compare_at_price && (
                          <span className="ml-2 text-lg text-gray-500 line-through">
                            {formatCurrency(product.compare_at_price, product.currency || 'GMD')}
                          </span>
                        )}
                      </div>

                      <div className="mt-4 flex items-center text-sm text-gray-600">
                        <Clock className="mr-1.5 h-4 w-4 text-primary-500" />
                        <span>
                          Ends in {product.timeLeft || `${getRandomHours()}h ${getRandomMinutes()}m`}
                        </span>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          )}


        </div>
      </div>
    </section>
  );
}
