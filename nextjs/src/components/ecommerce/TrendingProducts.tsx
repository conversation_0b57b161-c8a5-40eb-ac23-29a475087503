"use client";

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRight, TrendingUp } from 'lucide-react';
import { Product } from '@/lib/types/ecommerce';
import { EcommerceClientService } from '@/lib/services/ecommerce-client';
import { formatCurrency } from '@/lib/utils/format';

interface TrendingProductsProps {
  title?: string;
  subtitle?: string;
  viewAllLink?: string;
  initialProducts?: Product[];
  limit?: number;
}

export default function TrendingProducts({
  title = "Trending Products",
  subtitle = "Discover what's popular right now",
  viewAllLink = "/trending",
  initialProducts,
  limit = 4
}: TrendingProductsProps) {
  const [products, setProducts] = useState<Product[]>(initialProducts || []);
  const [loading, setLoading] = useState(!initialProducts);

  useEffect(() => {
    if (!initialProducts) {
      const fetchProducts = async () => {
        try {
          // Get featured products
          const trendingProducts = await EcommerceClientService.getFeaturedProducts(limit);
          setProducts(trendingProducts);
        } catch (error) {
          console.error('Error fetching trending products:', error);
        } finally {
          setLoading(false);
        }
      };

      fetchProducts();
    }
  }, [initialProducts, limit]);

  return (
    <section className="bg-white py-12 sm:py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight text-gray-900 sm:text-3xl">
              {title}
            </h2>
            <p className="mt-2 text-lg text-gray-600">
              {subtitle}
            </p>
          </div>

          {viewAllLink && (
            <Link
              href={viewAllLink}
              className="hidden items-center text-sm font-medium text-primary-600 hover:text-primary-700 sm:flex"
            >
              View all
              <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          )}
        </div>

        <div className="mt-10">
          {loading ? (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
              {Array.from({ length: limit }).map((_, index) => (
                <div key={index} className="h-96 animate-pulse rounded-lg bg-gray-200"></div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
              {products.map((product) => {
                const salePrice = product.salePrice || product.compare_at_price;

                return (
                  <Link
                    key={product.id}
                    href={`/products/${product.slug || product.id}`}
                    className="group"
                  >
                    <div className="overflow-hidden rounded-lg bg-white shadow transition-all duration-300 hover:shadow-md">
                      <div className="relative">
                        {/* Product image */}
                        <div className="relative h-64 overflow-hidden">
                          {(product.image_url || product.imageUrl) ? (
                            <Image
                              src={product.image_url || product.imageUrl || ''}
                              alt={product.name}
                              fill
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                              className="object-cover object-center transition-transform duration-300 group-hover:scale-105"
                            />
                          ) : (
                            <div className="flex h-full items-center justify-center bg-gray-200">
                              <span className="text-gray-400">No image</span>
                            </div>
                          )}
                        </div>

                        {/* Trending badge */}
                        <div className="absolute left-3 top-3 flex items-center rounded-full bg-white px-2 py-1 text-xs font-medium text-primary-600 shadow">
                          <TrendingUp className="mr-1 h-3 w-3" />
                          Trending
                        </div>
                      </div>

                      <div className="p-4">
                        <h3 className="mb-1 text-lg font-medium text-gray-900 line-clamp-1">
                          {product.name}
                        </h3>

                        <p className="mb-2 text-sm text-gray-600 line-clamp-2">
                          {product.description}
                        </p>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            {salePrice ? (
                              <>
                                <span className="text-lg font-bold text-primary-600">
                                  {formatCurrency(salePrice)}
                                </span>
                                <span className="ml-2 text-sm text-gray-500 line-through">
                                  {formatCurrency(product.price)}
                                </span>
                              </>
                            ) : (
                              <span className="text-lg font-bold text-gray-900">
                                {formatCurrency(product.price)}
                              </span>
                            )}
                          </div>

                          <div className="rounded-full bg-primary-50 px-2 py-1 text-xs font-medium text-primary-700">
                            Popular
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          )}

          {viewAllLink && (
            <div className="mt-8 text-center sm:hidden">
              <Link
                href={viewAllLink}
                className="inline-flex items-center rounded-md bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700"
              >
                View all trending products
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}
