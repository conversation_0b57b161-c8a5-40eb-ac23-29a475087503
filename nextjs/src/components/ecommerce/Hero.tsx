"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Pause, Search } from 'lucide-react';
import { getActiveSlides } from '@/features/admin/api';
import { HeroSlide } from '@/features/admin/types';

// Define category types for fallback
interface Category {
  name: string;
  href: string;
  image: string;
}

export default function Hero() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [slides, setSlides] = useState<HeroSlide[]>([]);
  const [loading, setLoading] = useState(true);

  // Fallback categories if no slides are found
  const fallbackCategories: Category[] = [
    {
      name: 'Motors',
      href: '/categories/motors',
      image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?ixlib=rb-4.0.3'
    },
    {
      name: 'Electronics',
      href: '/categories/electronics',
      image: 'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?ixlib=rb-4.0.3'
    },
    {
      name: 'Collectibles and Art',
      href: '/categories/collectibles-art',
      image: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?ixlib=rb-4.0.3'
    }
  ];

  // Fetch slides from database
  useEffect(() => {
    const fetchSlides = async () => {
      try {
        const activeSlides = await getActiveSlides();
        setSlides(activeSlides);
      } catch (error) {
        console.error('Error fetching slides:', error);
        setSlides([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSlides();
  }, []);

  // Use slides from database or fallback to categories
  const displayItems = slides.length > 0 ? slides : fallbackCategories.map((cat, index) => ({
    id: `fallback-${index}`,
    title: cat.name,
    subtitle: `Discover amazing ${cat.name.toLowerCase()}`,
    image_url: cat.image,
    link_url: cat.href,
    link_text: 'Find now',
    background_color: '#F78100',
    text_color: '#FFFFFF',
    position: index + 1,
    is_active: true,
    created_at: '',
    updated_at: ''
  }));

  const togglePause = () => {
    setIsPaused(!isPaused);
  };

  // Auto-advance slides
  useEffect(() => {
    if (isPaused || loading || displayItems.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev === displayItems.length - 1 ? 0 : prev + 1));
    }, 5000);

    return () => clearInterval(interval);
  }, [isPaused, loading, displayItems.length]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (loading || displayItems.length <= 1) return;

      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          setCurrentSlide((prev) => (prev === 0 ? displayItems.length - 1 : prev - 1));
          break;
        case 'ArrowRight':
          event.preventDefault();
          setCurrentSlide((prev) => (prev === displayItems.length - 1 ? 0 : prev + 1));
          break;
        case ' ':
          event.preventDefault();
          setIsPaused((prev) => !prev);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [loading, displayItems.length]);

  const SlideContent = ({ item, index }: { item: any; index: number }) => (
    <div key={item.id || index} className="w-full flex-shrink-0">
      <Link href={item.link_url || '#'} className="block group">
        <div className="relative h-64 sm:h-72 lg:h-80 overflow-hidden rounded-2xl">
          <Image
            src={item.image_url}
            alt={item.title}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 40vw"
            className="object-cover object-center transition-transform duration-300 group-hover:scale-105"
            priority={index === 0}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
          <div className="absolute bottom-0 left-0 right-0 p-4 lg:p-6">
            <h3 className="text-xl lg:text-2xl font-bold text-white mb-2">
              {item.title}
            </h3>
            {item.subtitle && (
              <p className="text-sm lg:text-base text-white/90 mb-3">
                {item.subtitle}
              </p>
            )}
            <span className="inline-flex items-center text-sm lg:text-base text-white/80 group-hover:text-white transition-colors">
              {item.link_text || 'Learn More'}
              <svg className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </span>
          </div>
        </div>
      </Link>
    </div>
  );

  const NavigationDots = () => (
    <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
      {displayItems.map((_, index) => (
        <button
          key={index}
          className={`h-2 w-2 rounded-full transition-all duration-200 ${
            currentSlide === index 
              ? "bg-white w-6" 
              : "bg-white/40 hover:bg-white/60"
          }`}
          onClick={() => setCurrentSlide(index)}
          aria-label={`Go to slide ${index + 1}`}
        />
      ))}
    </div>
  );

  const LoadingSpinner = () => (
    <div className="flex items-center justify-center h-64 sm:h-72 lg:h-80">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
    </div>
  );

  return (
    <div className="relative mt-10 overflow-hidden bg-[#F78100] rounded-3xl mx-auto max-w-7xl">
      <div className="px-4 py-16 sm:px-6 lg:py-20 lg:px-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 lg:gap-12">
          {/* Text content */}
          <div className="flex flex-col justify-center">
            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl">
              Find what you're looking for
            </h1>

            <p className="mt-4 max-w-xl text-xl text-white/90">
              Discover amazing products, great deals, and everything you need in
              one place.
            </p>

            {/* CTA Button */}
            <div className="mt-8">
              <Link
                href="/products"
                className="inline-flex items-center rounded-full bg-black px-6 py-3 text-base font-medium text-white shadow-md transition-all hover:bg-gray-900 hover:shadow-lg"
              >
                Start Finding
              </Link>
            </div>

            {/* Find bar - Mobile only */}
            <div className="mt-8 flex w-full max-w-md rounded-lg border border-white/30 bg-white/10 shadow-sm backdrop-blur-sm lg:hidden">
              <div className="flex items-center pl-4">
                <Search className="h-5 w-5 text-white" />
              </div>
              <input
                type="text"
                placeholder="Find products..."
                className="w-full border-0 bg-transparent py-3 pl-3 pr-4 text-white placeholder-white/70 focus:outline-none focus:ring-0"
              />
              <button className="m-1 rounded-md bg-black px-4 py-2 text-sm font-medium text-white hover:bg-gray-900 transition-colors">
                Find
              </button>
            </div>
          </div>

          {/* Category showcase - Desktop Slider */}
          <div className="hidden lg:block relative">
            {loading ? (
              <LoadingSpinner />
            ) : (
              <div className="relative overflow-hidden rounded-2xl">
                <div
                  className="flex transition-transform duration-500 ease-in-out"
                  style={{ transform: `translateX(-${currentSlide * 100}%)` }}
                >
                  {displayItems.map((item, index) => (
                    <SlideContent key={item.id || index} item={item} index={index} />
                  ))}
                </div>

                {/* Desktop Navigation Controls */}
                {displayItems.length > 1 && (
                  <>
                    <button
                      className="absolute right-4 top-4 rounded-full bg-black/30 p-2 text-white hover:bg-black/50 transition-colors backdrop-blur-sm"
                      onClick={togglePause}
                      title={isPaused ? 'Resume slideshow' : 'Pause slideshow'}
                      aria-label={isPaused ? 'Resume slideshow' : 'Pause slideshow'}
                    >
                      {isPaused ? (
                        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M8 5v14l11-7z" />
                        </svg>
                      ) : (
                        <Pause className="h-4 w-4" />
                      )}
                    </button>

                    <NavigationDots />
                  </>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Featured categories slider (mobile view) */}
        <div className="mt-12 lg:hidden">
          <div className="relative overflow-hidden rounded-2xl">
            {loading ? (
              <LoadingSpinner />
            ) : (
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentSlide * 100}%)` }}
              >
                {displayItems.map((item, index) => (
                  <div key={item.id || index} className="w-full flex-shrink-0">
                    <SlideContent item={item} index={index} />
                  </div>
                ))}
              </div>
            )}

            {/* Mobile Navigation controls */}
            {!loading && displayItems.length > 1 && (
              <>
                <NavigationDots />

                <button
                  className="absolute right-2 top-2 rounded-full bg-black/30 p-2 text-white hover:bg-black/50 transition-colors backdrop-blur-sm"
                  onClick={togglePause}
                  title={isPaused ? 'Resume slideshow' : 'Pause slideshow'}
                  aria-label={isPaused ? 'Resume slideshow' : 'Pause slideshow'}
                >
                  {isPaused ? (
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z" />
                    </svg>
                  ) : (
                    <Pause className="h-4 w-4" />
                  )}
                </button>

                <div className="absolute left-2 top-2 rounded-full bg-black/30 px-2 py-1 text-white text-xs backdrop-blur-sm">
                  {currentSlide + 1}/{displayItems.length}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}