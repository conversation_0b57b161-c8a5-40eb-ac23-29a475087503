import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, DollarSign, TrendingUp } from 'lucide-react';
import { formatCurrency } from '@/lib/utils/format';

interface PayoutSchedule {
  id: string;
  payout_date: string;
  total_amount: number;
  service_fees_deducted: number;
  net_amount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
}

interface StoreOwnerPayoutCardProps {
  upcomingPayouts: PayoutSchedule[];
  totalPendingAmount: number;
  nextPayoutDate: string | null;
  className?: string;
}

const StoreOwnerPayoutCard: React.FC<StoreOwnerPayoutCardProps> = ({
  upcomingPayouts,
  totalPendingAmount,
  nextPayoutDate,
  className = ''
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <DollarSign className="h-5 w-5 text-green-600" />
          <span>Payout Schedule</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">Pending Amount</span>
            </div>
            <p className="text-2xl font-bold text-green-900">
              {formatCurrency(totalPendingAmount, 'GMD')}
            </p>
          </div>

          {nextPayoutDate && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Calendar className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Next Payout</span>
              </div>
              <p className="text-lg font-semibold text-blue-900">
                {formatDate(nextPayoutDate)}
              </p>
            </div>
          )}
        </div>

        {/* Payout Schedule */}
        <div>
          <h4 className="font-medium mb-3">Upcoming Payouts</h4>
          {upcomingPayouts.length > 0 ? (
            <div className="space-y-3">
              {upcomingPayouts.slice(0, 5).map((payout) => (
                <div key={payout.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">{formatDate(payout.payout_date)}</span>
                      <Badge className={getStatusColor(payout.status)}>
                        {payout.status}
                      </Badge>
                    </div>
                    <div className="mt-1 text-sm text-gray-600">
                      Gross: {formatCurrency(payout.total_amount, 'GMD')} | 
                      Service Fee: {formatCurrency(payout.service_fees_deducted, 'GMD')}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      {formatCurrency(payout.net_amount, 'GMD')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6 text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-2 text-gray-300" />
              <p>No upcoming payouts scheduled</p>
            </div>
          )}
        </div>

        {/* Payout Info */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h5 className="font-medium mb-2">Payout Information</h5>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Payouts are processed every Monday and Friday</li>
            <li>• Service fees are automatically deducted</li>
            <li>• Processing typically takes 1-2 business days</li>
            <li>• You'll receive notifications when payouts are processed</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default StoreOwnerPayoutCard;
