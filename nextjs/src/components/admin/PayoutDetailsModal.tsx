'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { formatCurrency } from '@/utils/formatting/currency';
import { AdminPayout } from '@/features/admin/types';
import { confirmPayoutSent } from '@/features/admin/api';
import { CheckCircle, Download, Send, Receipt } from 'lucide-react';
import { useToast } from '@/lib/hooks/use-toast';

interface PayoutDetailsModalProps {
  payout: AdminPayout | null;
  isOpen: boolean;
  onClose: () => void;
  onPayoutUpdated?: () => void;
}

export default function PayoutDetailsModal({
  payout,
  isOpen,
  onClose,
  onPayoutUpdated
}: PayoutDetailsModalProps) {
  const [transactionId, setTransactionId] = useState('');
  const [isConfirming, setIsConfirming] = useState(false);
  const [receipt, setReceipt] = useState<any>(null);
  const { toast } = useToast();

  if (!payout) return null;

  const handleConfirmSent = async () => {
    if (!transactionId.trim()) {
      toast({
        title: "Transaction ID Required",
        description: "Please enter a transaction ID to confirm the payout.",
        variant: "destructive"
      });
      return;
    }

    setIsConfirming(true);
    try {
      const result = await confirmPayoutSent(payout.id, transactionId);

      if (result.success) {
        setReceipt(result.receipt);
        toast({
          title: "Payout Confirmed",
          description: "The payout has been marked as sent and a receipt has been generated.",
        });

        if (onPayoutUpdated) {
          onPayoutUpdated();
        }
      } else {
        throw new Error('Failed to confirm payout');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to confirm payout. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsConfirming(false);
    }
  };

  const downloadReceipt = () => {
    if (!receipt) return;

    const receiptContent = `
FINDER MARKETPLACE - PAYOUT RECEIPT
=====================================

Receipt ID: ${receipt.id}
Generated: ${new Date(receipt.generated_at).toLocaleString()}

STORE INFORMATION
-----------------
Store: ${receipt.store_name}
Email: ${receipt.store_email}

ORDER INFORMATION
-----------------
Order ID: ${receipt.order_id}
Transaction ID: ${receipt.transaction_id}

PAYOUT DETAILS
--------------
Gross Amount: ${formatCurrency(receipt.amount + receipt.commission, receipt.currency)}
Finder Commission (5%): ${formatCurrency(receipt.commission, receipt.currency)}
Net Payout: ${formatCurrency(receipt.amount, receipt.currency)}

ITEMS
-----
${receipt.items.map((item: any) =>
  `${item.product_name} x${item.quantity} - ${formatCurrency(item.total, receipt.currency)}`
).join('\n')}

=====================================
Thank you for using Finder Marketplace
    `;

    const blob = new Blob([receiptContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `receipt-${receipt.id}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'sent': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Receipt className="h-5 w-5" />
            Payout Details
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Payout Status */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Status</h3>
            <Badge className={getStatusColor(payout.payout_status)}>
              {payout.payout_status.toUpperCase()}
            </Badge>
          </div>

          {/* Store Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Store Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Store:</span>
                <span className="font-medium">{payout.store_name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Order ID:</span>
                <span className="font-mono text-sm">{payout.order_id}</span>
              </div>
            </CardContent>
          </Card>

          {/* Financial Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Financial Breakdown</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Gross Amount:</span>
                <span>{formatCurrency(payout.amount + payout.commission, payout.currency)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Finder Commission (5%):</span>
                <span className="text-red-600">-{formatCurrency(payout.commission, payout.currency)}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-semibold text-lg">
                <span>Net Payout:</span>
                <span className="text-green-600">{formatCurrency(payout.amount, payout.currency)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Transaction Information */}
          {payout.transaction_id && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Transaction Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between">
                  <span className="text-gray-600">Transaction ID:</span>
                  <span className="font-mono text-sm">{payout.transaction_id}</span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Receipt Section */}
          {receipt && (
            <Card className="border-green-200 bg-green-50">
              <CardHeader>
                <CardTitle className="text-base text-green-800 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Receipt Generated
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-green-700">Receipt ID: {receipt.id}</p>
                    <p className="text-xs text-green-600">Generated: {new Date(receipt.generated_at).toLocaleString()}</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadReceipt}
                    className="text-green-700 border-green-300 hover:bg-green-100"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Section */}
          {payout.payout_status === 'pending' && !receipt && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Confirm Payout Sent</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="transactionId">Transaction ID</Label>
                  <Input
                    id="transactionId"
                    value={transactionId}
                    onChange={(e) => setTransactionId(e.target.value)}
                    placeholder="Enter transaction ID from payment system"
                    className="mt-1"
                  />
                </div>
                <Button
                  onClick={handleConfirmSent}
                  disabled={isConfirming || !transactionId.trim()}
                  className="w-full"
                >
                  {isConfirming ? (
                    "Confirming..."
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Confirm Payment Sent
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Close Button */}
          <div className="flex justify-end">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
