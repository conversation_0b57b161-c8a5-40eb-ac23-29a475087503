import React from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  iconColor?: string;
  valuePrefix?: string;
  valueSuffix?: string;
  href?: string;
}

export default function StatsCard({
  title,
  value,
  icon: Icon,
  description,
  trend,
  iconColor = 'text-primary-500',
  valuePrefix = '',
  valueSuffix = '',
  href,
}: StatsCardProps) {
  const cardContent = (
    <CardContent className="p-6">
      <div className="flex justify-between items-start">
        <div>
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <h3 className="text-2xl font-bold mt-1">
            {valuePrefix}
            {value}
            {valueSuffix}
          </h3>
          {description && (
            <p className="text-sm text-gray-500 mt-1">{description}</p>
          )}
          {trend && (
            <p className={`text-sm mt-2 flex items-center ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
              {trend.isPositive ? '↑' : '↓'} {Math.abs(trend.value)}%
              <span className="text-gray-500 ml-1">from last period</span>
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full bg-gray-100 ${iconColor}`}>
          <Icon className="h-5 w-5" />
        </div>
      </div>
    </CardContent>
  );

  if (href) {
    return (
      <Link href={href}>
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          {cardContent}
        </Card>
      </Link>
    );
  }

  return (
    <Card>
      {cardContent}
    </Card>
  );
}
