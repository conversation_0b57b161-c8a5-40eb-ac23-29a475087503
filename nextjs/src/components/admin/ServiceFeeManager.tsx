'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/lib/hooks/use-toast';
import { SystemSettingsService, ServiceFeeConfig } from '@/lib/services/system-settings';
import { DollarSign, Percent, TrendingUp, Info, Plus, Trash2 } from 'lucide-react';

interface ServiceFeeManagerProps {
  onConfigUpdate?: (config: ServiceFeeConfig) => void;
}

export function ServiceFeeManager({ onConfigUpdate }: ServiceFeeManagerProps) {
  const { toast } = useToast();
  const [config, setConfig] = useState<ServiceFeeConfig>({
    fee_type: 'tiered',
    fixed_fee: 5.00,
    percentage_fee: 3.0,
    tiers: [
      { min_amount: 0, max_amount: 200, fee: 2.00 },
      { min_amount: 201, max_amount: null, fee: 5.00 }
    ],
    grace_period_days: 7
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [previewAmount, setPreviewAmount] = useState(100);
  const [previewFee, setPreviewFee] = useState(0);

  useEffect(() => {
    loadConfig();
  }, []);

  useEffect(() => {
    calculatePreviewFee();
  }, [config, previewAmount]);

  const loadConfig = async () => {
    try {
      const loadedConfig = await SystemSettingsService.getServiceFeeConfig();
      
      // If no config exists, create a default one
      if (!loadedConfig) {
        const defaultConfig = {
          fee_type: 'tiered' as const,
          tiers: [
            { min_amount: 0, max_amount: 1000, fee: 10.00 },
            { min_amount: 1001, max_amount: null, fee: 25.00 }
          ],
          grace_period_days: 7
        };
        setConfig(defaultConfig);
      } else {
        setConfig(loadedConfig);
      }
    } catch (error) {
      console.error('Error loading service fee config:', error);
      toast({
        title: 'Error',
        description: 'Failed to load service fee configuration',
        variant: 'destructive',
      });
      
      // Set a default config on error
      const defaultConfig = {
        fee_type: 'tiered' as const,
        tiers: [
          { min_amount: 0, max_amount: 1000, fee: 10.00 },
          { min_amount: 1001, max_amount: null, fee: 25.00 }
        ],
        grace_period_days: 7
      };
      setConfig(defaultConfig);
    } finally {
      setLoading(false);
    }
  };

  const calculatePreviewFee = () => {
    let fee = 0;
    
    switch (config.fee_type) {
      case 'fixed':
        fee = config.fixed_fee || 0;
        break;
      case 'percentage':
        fee = (previewAmount * (config.percentage_fee || 0)) / 100;
        break;
      case 'tiered':
        if (config.tiers) {
          for (const tier of config.tiers) {
            const meetsMin = previewAmount >= tier.min_amount;
            const meetsMax = tier.max_amount === null || previewAmount <= tier.max_amount;
            
            if (meetsMin && meetsMax) {
              if (tier.fee !== undefined) {
                fee = tier.fee;
              } else if (tier.percentage !== undefined) {
                fee = (previewAmount * tier.percentage) / 100;
              }
              break;
            }
          }
        }
        break;
    }
    
    setPreviewFee(fee);
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const success = await SystemSettingsService.updateServiceFeeConfig({
        ...config,
        effective_date: new Date().toISOString()
      });

      if (success) {
        toast({
          title: 'Success',
          description: 'Service fee configuration updated successfully',
          variant: 'default',
        });
        onConfigUpdate?.(config);
      } else {
        throw new Error('Failed to update configuration');
      }
    } catch (error) {
      console.error('Error saving service fee config:', error);
      toast({
        title: 'Error',
        description: 'Failed to save service fee configuration',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const addTier = () => {
    const lastTier = config?.tiers?.[config.tiers.length - 1];
    const newMinAmount = lastTier?.max_amount ? lastTier.max_amount + 1 : 0;
    
    setConfig(prev => ({
      ...prev,
      tiers: [
        ...(prev.tiers || []),
        { min_amount: newMinAmount, max_amount: null, fee: 5.00 }
      ]
    }));
  };

  const removeTier = (index: number) => {
    setConfig(prev => ({
      ...prev,
      tiers: prev.tiers?.filter((_, i) => i !== index) || []
    }));
  };

  const updateTier = (index: number, field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      tiers: prev.tiers?.map((tier, i) => 
        i === index ? { ...tier, [field]: value } : tier
      ) || []
    }));
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading configuration...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Service Fee Configuration
          </CardTitle>
          <CardDescription>
            Configure how service fees are calculated for transactions. Changes apply to new transactions with a grace period for pending ones.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Fee Type Selection */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Fee Structure</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div 
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  config?.fee_type === 'fixed' 
                    ? 'border-primary-500 bg-primary-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setConfig(prev => ({ ...prev, fee_type: 'fixed' }))}
              >
                <div className="flex items-center gap-2 mb-2">
                  <DollarSign className="h-4 w-4" />
                  <span className="font-medium">Fixed Fee</span>
                </div>
                <p className="text-sm text-gray-600">Same fee for all transactions</p>
              </div>
              
              <div 
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  config?.fee_type === 'percentage' 
                    ? 'border-primary-500 bg-primary-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setConfig(prev => ({ ...prev, fee_type: 'percentage' }))}
              >
                <div className="flex items-center gap-2 mb-2">
                  <Percent className="h-4 w-4" />
                  <span className="font-medium">Percentage</span>
                </div>
                <p className="text-sm text-gray-600">Percentage of transaction amount</p>
              </div>
              
              <div 
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  config?.fee_type === 'tiered' 
                    ? 'border-primary-500 bg-primary-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setConfig(prev => ({ ...prev, fee_type: 'tiered' }))}
              >
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="h-4 w-4" />
                  <span className="font-medium">Tiered</span>
                </div>
                <p className="text-sm text-gray-600">Different fees based on amount ranges</p>
              </div>
            </div>
          </div>

          {/* Configuration based on selected type */}
          {config?.fee_type === 'fixed' && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="fixed-fee">Fixed Fee Amount ($)</Label>
                <Input
                  id="fixed-fee"
                  type="number"
                  step="0.01"
                  min="0"
                  value={config?.fixed_fee || 0}
                  onChange={(e) => setConfig(prev => ({ 
                    ...prev, 
                    fixed_fee: parseFloat(e.target.value) || 0 
                  }))}
                  className="mt-1"
                />
              </div>
            </div>
          )}

          {config?.fee_type === 'percentage' && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="percentage-fee">Percentage Fee (%)</Label>
                <Input
                  id="percentage-fee"
                  type="number"
                  step="0.1"
                  min="0"
                  max="100"
                  value={config?.percentage_fee || 0}
                  onChange={(e) => setConfig(prev => ({ 
                    ...prev, 
                    percentage_fee: parseFloat(e.target.value) || 0 
                  }))}
                  className="mt-1"
                />
              </div>
            </div>
          )}

          {config?.fee_type === 'tiered' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">Fee Tiers</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addTier}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Tier
                </Button>
              </div>
              
              <div className="space-y-3">
                {config?.tiers?.map((tier, index) => (
                  <div key={index} className="p-4 border rounded-lg space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Tier {index + 1}</span>
                      {config?.tiers && config.tiers.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeTier(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                      <div>
                        <Label>Min Amount ($)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          value={tier.min_amount}
                          onChange={(e) => updateTier(index, 'min_amount', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                      
                      <div>
                        <Label>Max Amount ($)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          value={tier.max_amount || ''}
                          placeholder="No limit"
                          onChange={(e) => updateTier(index, 'max_amount', e.target.value ? parseFloat(e.target.value) : null)}
                        />
                      </div>
                      
                      <div>
                        <Label>Fixed Fee ($)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          value={tier.fee || ''}
                          onChange={(e) => updateTier(index, 'fee', e.target.value ? parseFloat(e.target.value) : undefined)}
                        />
                      </div>
                      
                      <div>
                        <Label>Percentage (%)</Label>
                        <Input
                          type="number"
                          step="0.1"
                          min="0"
                          max="100"
                          value={tier.percentage || ''}
                          onChange={(e) => updateTier(index, 'percentage', e.target.value ? parseFloat(e.target.value) : undefined)}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Grace Period */}
          <div>
            <Label htmlFor="grace-period">Grace Period (days)</Label>
            <Input
              id="grace-period"
              type="number"
              min="0"
              value={config?.grace_period_days || 0}
              onChange={(e) => setConfig(prev => ({ 
                ...prev, 
                grace_period_days: parseInt(e.target.value) || 0 
              }))}
              className="mt-1"
            />
            <p className="text-sm text-gray-600 mt-1">
              Number of days to apply old rates to pending transactions after changes
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Preview Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Fee Preview
          </CardTitle>
          <CardDescription>
            Test how fees will be calculated with the current configuration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="preview-amount">Transaction Amount ($)</Label>
            <Input
              id="preview-amount"
              type="number"
              step="0.01"
              min="0"
              value={previewAmount}
              onChange={(e) => setPreviewAmount(parseFloat(e.target.value) || 0)}
              className="mt-1"
            />
          </div>
          
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="font-medium">Transaction Amount:</span>
              <span>${previewAmount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium">Service Fee:</span>
              <span className="text-primary-600 font-semibold">${previewFee.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center border-t pt-2 mt-2">
              <span className="font-medium">Net Amount:</span>
              <span className="font-semibold">${(previewAmount - previewFee).toFixed(2)}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={saving}
          className="flex items-center gap-2"
        >
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Saving...
            </>
          ) : (
            <>
              <DollarSign className="h-4 w-4" />
              Save Configuration
            </>
          )}
        </Button>
      </div>
    </div>
  );
}