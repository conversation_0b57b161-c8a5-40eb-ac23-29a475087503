'use client';

import React, { useState, useEffect } from 'react';
import { Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { createSPASassClient } from '@/lib/supabase/client';

interface OrderItem {
  id: string;
  product_id: string;
  store_id: string;
  product_name: string;
  store_name: string;
  quantity: number;
}

interface OrderRatingButtonProps {
  orderId: string;
  orderStatus: string;
}

interface ProductRatingModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderItem: OrderItem;
  onSubmit: (rating: number, title?: string, content?: string) => Promise<void>;
}

function ProductRatingModal({ isOpen, onClose, orderItem, onSubmit }: ProductRatingModalProps) {
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (rating === 0) {
      alert('Please provide a rating');
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(rating, title.trim() || undefined, content.trim() || undefined);
      onClose();
    } catch (error) {
      console.error('Error submitting rating:', error);
      alert('Failed to submit rating. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStars = () => (
    <div className="flex gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <button
          key={star}
          type="button"
          onClick={() => setRating(star)}
          onMouseEnter={() => setHoveredRating(star)}
          onMouseLeave={() => setHoveredRating(0)}
          className="hover:scale-110 transition-transform"
        >
          <Star
            className={`w-6 h-6 ${
              star <= (hoveredRating || rating)
                ? 'text-yellow-400 fill-yellow-400'
                : 'text-gray-300'
            }`}
          />
        </button>
      ))}
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-xl font-bold text-gray-900">Rate Product</h2>
              <p className="text-sm text-gray-600">{orderItem.product_name}</p>
              <p className="text-xs text-gray-500">from {orderItem.store_name}</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Rating */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Your Rating *
              </label>
              {renderStars()}
            </div>

            {/* Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Review Title (optional)
              </label>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Summarize your experience"
                maxLength={100}
              />
            </div>

            {/* Content */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Your Review (optional)
              </label>
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Tell others about this product..."
                maxLength={500}
              />
              <p className="text-xs text-gray-500 mt-1">
                {content.length}/500 characters
              </p>
            </div>

            {/* Submit Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || rating === 0}
                className="flex-1"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Rating'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default function OrderRatingButton({ orderId, orderStatus }: OrderRatingButtonProps) {
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [reviewedProducts, setReviewedProducts] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);
  const [selectedOrderItem, setSelectedOrderItem] = useState<OrderItem | null>(null);
  const [showRatingModal, setShowRatingModal] = useState(false);

  useEffect(() => {
    loadOrderItemsAndReviews();
  }, [orderId]);

  const loadOrderItemsAndReviews = async () => {
    setLoading(true);
    
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setLoading(false);
        return;
      }

      // Get order items with product and store names
      const { data: orderItemsData, error: orderItemsError } = await supabase
        .from('order_items')
        .select(`
          id,
          product_id,
          store_id,
          quantity,
          products(name),
          stores(name)
        `)
        .eq('order_id', orderId);

      if (orderItemsError) throw orderItemsError;

      // Transform the data
      const items: OrderItem[] = (orderItemsData || []).map(item => ({
        id: item.id,
        product_id: item.product_id,
        store_id: item.store_id,
        product_name: item.products?.name || 'Unknown Product',
        store_name: 'Finder Marketplace',
        quantity: item.quantity
      }));

      setOrderItems(items);

      // Check which products have already been reviewed
      if (items.length > 0) {
        const productIds = items.map(item => item.product_id);
        const { data: reviewsData, error: reviewsError } = await supabase
          .from('reviews')
          .select('product_id')
          .in('product_id', productIds)
          .eq('user_id', user.id);

        if (reviewsError) throw reviewsError;

        const reviewedProductIds = new Set(
          (reviewsData || []).map(review => review.product_id)
        );
        setReviewedProducts(reviewedProductIds);
      }
    } catch (error) {
      console.error('Error loading order items and reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitRating = async (rating: number, title?: string, content?: string) => {
    if (!selectedOrderItem) return;

    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        alert('You must be logged in to submit a review.');
        return;
      }

      const { error } = await supabase
        .from('reviews')
        .insert({
          user_id: user.id,
          product_id: selectedOrderItem.product_id,
          store_id: selectedOrderItem.store_id,
          rating,
          title: title || null,
          content: content || null
        });

      if (error) throw error;

      // Update local state
      setReviewedProducts(prev => new Set([...prev, selectedOrderItem.product_id]));

      alert('Review submitted successfully!');
    } catch (error: any) {
      console.error('Error submitting rating:', error);
      if (error.code === '23505') {
        alert('You have already reviewed this product.');
      } else {
        throw error;
      }
    }
  };

  const handleRateProduct = (orderItem: OrderItem) => {
    setSelectedOrderItem(orderItem);
    setShowRatingModal(true);
  };

  // Only show for delivered orders
  if (!orderStatus || !['delivered', 'completed'].includes(orderStatus.toLowerCase()) || loading) {
    return null;
  }

  const unratedItems = orderItems.filter(item => !reviewedProducts.has(item.product_id));

  if (unratedItems.length === 0 && orderItems.length > 0) {
    return (
      <div className="flex items-center gap-2 text-sm text-green-600">
        <Star className="w-4 h-4 fill-green-600" />
        <span>You've rated all products from this order</span>
      </div>
    );
  }

  if (unratedItems.length === 0) {
    return null;
  }

  return (
    <>
      <div className="space-y-2">
        <p className="text-sm font-medium text-gray-700">Rate your products:</p>
        <div className="space-y-2">
          {unratedItems.map((item) => (
            <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">{item.product_name}</p>
                <p className="text-xs text-gray-500">from {item.store_name}</p>
                {item.quantity > 1 && (
                  <p className="text-xs text-gray-400">Quantity: {item.quantity}</p>
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleRateProduct(item)}
                className="flex items-center gap-2"
              >
                <Star className="w-4 h-4" />
                Rate
              </Button>
            </div>
          ))}
        </div>
      </div>

      {selectedOrderItem && (
        <ProductRatingModal
          isOpen={showRatingModal}
          onClose={() => {
            setShowRatingModal(false);
            setSelectedOrderItem(null);
          }}
          orderItem={selectedOrderItem}
          onSubmit={handleSubmitRating}
        />
      )}
    </>
  );
}