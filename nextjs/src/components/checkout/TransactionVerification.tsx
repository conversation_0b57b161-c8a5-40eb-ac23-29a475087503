import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

interface TransactionVerificationProps {
  paymentMethod: 'wave' | 'crypto';
  onVerify: (transactionId: string) => void;
}

const TransactionVerification: React.FC<TransactionVerificationProps> = ({
  paymentMethod,
  onVerify,
}) => {
  const [transactionId, setTransactionId] = useState('');
  const [confirmed, setConfirmed] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!transactionId.trim()) {
      setError('Please enter your transaction ID');
      return;
    }
    
    if (!confirmed) {
      setError('Please confirm that you have sent the payment');
      return;
    }
    
    onVerify(transactionId.trim());
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold mb-4">
        Verify Your {paymentMethod === 'wave' ? 'Wave' : 'Crypto'} Payment
      </h2>
      
      <Alert className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Please enter your transaction ID to verify your payment.
        </AlertDescription>
      </Alert>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="transaction-id">
            {paymentMethod === 'wave' ? 'Wave Transaction ID' : 'Transaction Hash/ID'}
          </Label>
          <Input
            id="transaction-id"
            placeholder={paymentMethod === 'wave' ? 'e.g. WV123456789' : 'e.g. 0x1234...'}
            value={transactionId}
            onChange={(e) => {
              setTransactionId(e.target.value);
              if (error) setError('');
            }}
          />
          <p className="text-sm text-gray-500">
            {paymentMethod === 'wave' 
              ? 'You can find this in your Wave app under transaction history'
              : 'You can find this in your wallet after the transaction is confirmed'}
          </p>
        </div>
        
        <div className="flex items-start space-x-2">
          <Checkbox
            id="confirm"
            checked={confirmed}
            onCheckedChange={(checked) => {
              setConfirmed(checked as boolean);
              if (error) setError('');
            }}
          />
          <div className="grid gap-1.5 leading-none">
            <Label
              htmlFor="confirm"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              I confirm that I have sent the exact amount to the {paymentMethod === 'wave' ? 'Wave account' : 'wallet address'} shown
            </Label>
          </div>
        </div>
        
        {error && (
          <p className="text-sm text-red-500">{error}</p>
        )}
        
        <Button 
          type="submit"
          className="w-full bg-primary-600 hover:bg-primary-700"
        >
          Verify Payment
        </Button>
        
        <div className="text-center text-sm text-gray-500">
          <p>Your payment will be reviewed by our team.</p>
          <p className="mt-1">You will receive a notification once it's confirmed.</p>
        </div>
      </form>
    </div>
  );
};

export default TransactionVerification;
