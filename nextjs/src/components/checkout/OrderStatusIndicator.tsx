import React from 'react';
import { CheckCircle, Clock, Package, Truck, XCircle } from 'lucide-react';

interface OrderStatusIndicatorProps {
  status: string;
}

const OrderStatusIndicator: React.FC<OrderStatusIndicatorProps> = ({ status }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'pending':
        return {
          icon: <Clock className="h-8 w-8 text-yellow-500" />,
          label: 'Pending',
          description: 'Your payment is being reviewed',
          color: 'bg-yellow-100 text-yellow-800',
        };
      case 'processing':
        return {
          icon: <Package className="h-8 w-8 text-blue-500" />,
          label: 'Processing',
          description: 'Your order is being processed',
          color: 'bg-blue-100 text-blue-800',
        };
      case 'shipped':
        return {
          icon: <Truck className="h-8 w-8 text-indigo-500" />,
          label: 'Shipped',
          description: 'Your order is on its way',
          color: 'bg-indigo-100 text-indigo-800',
        };
      case 'delivered':
        return {
          icon: <CheckCircle className="h-8 w-8 text-green-500" />,
          label: 'Delivered',
          description: 'Your order has been delivered',
          color: 'bg-green-100 text-green-800',
        };
      case 'cancelled':
        return {
          icon: <XCircle className="h-8 w-8 text-red-500" />,
          label: 'Cancelled',
          description: 'Your order has been cancelled',
          color: 'bg-red-100 text-red-800',
        };
      default:
        return {
          icon: <Clock className="h-8 w-8 text-gray-500" />,
          label: 'Unknown',
          description: 'Status unknown',
          color: 'bg-gray-100 text-gray-800',
        };
    }
  };

  const { icon, label, description, color } = getStatusConfig();

  return (
    <div className={`p-4 rounded-lg ${color} flex items-center`}>
      <div className="mr-4">
        {icon}
      </div>
      <div>
        <h3 className="font-medium text-lg">{label}</h3>
        <p>{description}</p>
      </div>
    </div>
  );
};

export default OrderStatusIndicator;
