import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Smartphone, Bitcoin, Building2 } from 'lucide-react';

interface PaymentMethodSelectorProps {
  onSelect: (method: 'wave' | 'crypto' | 'bank-transfer') => void;
}

const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({ onSelect }) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold mb-6">Select Payment Method</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Wave Payment Option */}
        <Card 
          className="cursor-pointer border-2 hover:border-primary-500 transition-colors"
          onClick={() => onSelect('wave')}
        >
          <CardContent className="p-6">
            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                <Smartphone className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-medium mb-2">Pay with Wave</h3>
              <p className="text-gray-500 text-sm mb-4">
                Scan our QR code with your Wave app or send to our Wave number
              </p>
              <Button 
                className="w-full bg-blue-600 hover:bg-blue-700"
                onClick={(e) => {
                  e.stopPropagation();
                  onSelect('wave');
                }}
              >
                Select Wave
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Crypto Payment Option */}
        <Card 
          className="cursor-pointer border-2 hover:border-primary-500 transition-colors"
          onClick={() => onSelect('crypto')}
        >
          <CardContent className="p-6">
            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-orange-100 flex items-center justify-center mb-4">
                <Bitcoin className="h-8 w-8 text-orange-600" />
              </div>
              <h3 className="text-lg font-medium mb-2">Pay with Crypto</h3>
              <p className="text-gray-500 text-sm mb-4">
                Send cryptocurrency to our wallet address or scan QR code
              </p>
              <Button 
                className="w-full bg-orange-600 hover:bg-orange-700"
                onClick={(e) => {
                  e.stopPropagation();
                  onSelect('crypto');
                }}
              >
                Select Crypto
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Bank Transfer Payment Option */}
        <Card 
          className="cursor-pointer border-2 hover:border-primary-500 transition-colors"
          onClick={() => onSelect('bank-transfer')}
        >
          <CardContent className="p-6">
            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
                <Building2 className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-lg font-medium mb-2">Bank Transfer</h3>
              <p className="text-gray-500 text-sm mb-4">
                Transfer directly to our bank account using your mobile banking app
              </p>
              <Button 
                className="w-full bg-green-600 hover:bg-green-700"
                onClick={(e) => {
                  e.stopPropagation();
                  onSelect('bank-transfer');
                }}
              >
                Select Bank Transfer
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mt-6 text-center text-sm text-gray-500">
        <p>All payments are securely processed and reviewed by our team.</p>
        <p className="mt-1">You will need to provide your transaction ID after payment.</p>
      </div>
    </div>
  );
};

export default PaymentMethodSelector;
