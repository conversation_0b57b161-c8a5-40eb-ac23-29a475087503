import { type NextRequest, NextResponse } from 'next/server'
import { updateSession } from '@/lib/supabase/middleware'

export async function middleware(request: NextRequest) {
    // Block public access to store pages
    if (request.nextUrl.pathname.startsWith('/stores')) {
        // Return 404 for store pages
        return NextResponse.rewrite(new URL('/404', request.url))
    }
    
    return await updateSession(request)
}

export const config = {
    matcher: [
        '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
    ],
}