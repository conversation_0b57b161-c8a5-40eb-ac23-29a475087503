'use client';

import React from 'react';
import EcommerceLayout from '@/components/ecommerce/EcommerceLayout';
import { OrderList, OrderStats } from '@/features/orders/components';
import { Package } from 'lucide-react';

export default function MyOrdersPage() {
  return (
    <EcommerceLayout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-3 mb-2">
              <Package className="h-8 w-8 text-primary-600" />
              <h1 className="text-3xl font-bold text-gray-900">My Orders</h1>
            </div>
            <p className="text-gray-600">
              Track and manage your orders
            </p>
          </div>

          {/* Order Statistics */}
          <div className="mb-8">
            <OrderStats />
          </div>

          {/* Orders List */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <OrderList />
          </div>
        </div>
      </div>
    </EcommerceLayout>
  );
}
