"use client";
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AdminHeader, ImageUpload } from '@/components/admin';
import { createStore } from '@/features/admin/api';
import { CreateStoreParams, StoreStatus } from '@/features/admin/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertCircle } from 'lucide-react';
import { slugify } from '@/lib/utils/string';

export default function NewStorePage() {
  const router = useRouter();
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<CreateStoreParams>({
    name: '',
    slug: '',
    description: '',
    owner_id: '',
    contact_email: '',
    contact_phone: '',
    status: 'active' as StoreStatus,
    featured: false,
    logo: '',
    cover_image: '',
    offers_delivery: true,
    delivery_fee: 0,
    delivery_radius_km: 10,
    delivery_time_estimate: '1-2 hours',
  });

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        // Use the admin API endpoint instead of the client-side function
        const response = await fetch('/api/admin/users');
        const data = await response.json();

        if (data.error) {
          throw new Error(data.error);
        }

        setUsers(data.users || []);
      } catch (error) {
        console.error('Error fetching users:', error);
        setError('Failed to load users');
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Auto-generate slug when name changes
    if (name === 'name') {
      setFormData((prev) => ({ ...prev, slug: slugify(value) }));
    }
  };

  const handleStatusChange = (status: StoreStatus) => {
    setFormData((prev) => ({ ...prev, status }));
  };

  const handleFeaturedChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, featured: checked }));
  };

  const handleOwnerChange = (ownerId: string) => {
    setFormData((prev) => ({ ...prev, owner_id: ownerId }));
  };

  const handleLogoUploaded = (url: string) => {
    setFormData((prev) => ({ ...prev, logo: url }));
  };

  const handleCoverImageUploaded = (url: string) => {
    setFormData((prev) => ({ ...prev, cover_image: url }));
  };

  const handleDeliveryChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, offers_delivery: checked }));
  };

  const handleNumberChange = (field: string, value: string) => {
    const numValue = parseFloat(value) || 0;
    setFormData((prev) => ({ ...prev, [field]: numValue }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      // Check if the selected user is a store_owner, if not, update their role
      const selectedUser = users.find(user => user.id === formData.owner_id);
      if (selectedUser && selectedUser.role !== 'store_owner') {
        try {
          // Use the admin API endpoint instead of the client-side function
          const response = await fetch(`/api/admin/users/${selectedUser.id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ role: 'store_owner' }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to update user role');
          }

          console.log(`Updated user ${selectedUser.email} to store_owner role`);
        } catch (updateError) {
          console.error('Error updating user role:', updateError);
          // Continue anyway, the store creation might still work
        }
      }

      const newStore = await createStore(formData);
      if (newStore) {
        router.push('/admin/stores');
        router.refresh();
      } else {
        setError('Failed to create store');
      }
    } catch (error) {
      console.error('Error creating store:', error);
      setError('An error occurred while creating the store');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Add New Store"
        description="Create a new store in your marketplace"
        backHref="/admin/stores"
      />

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Store Information</CardTitle>
                <CardDescription>
                  Enter the store&apos;s basic information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Store Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">Store Slug</Label>
                  <Input
                    id="slug"
                    name="slug"
                    value={formData.slug}
                    onChange={handleChange}
                    required
                  />
                  <p className="text-sm text-gray-500">
                    This will be used in the store&apos;s URL: /stores/{formData.slug}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={4}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="owner_id">Store Owner</Label>
                  <Select
                    value={formData.owner_id || undefined}
                    onValueChange={handleOwnerChange}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a store owner" />
                    </SelectTrigger>
                    <SelectContent>
                      {users.length > 0 ? (
                        users.map((user) => (
                          <SelectItem key={user.id} value={user.id}>
                            {user.email}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="placeholder" disabled>
                          No users available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
                <CardDescription>
                  Enter the store&apos;s contact details
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="contact_email">Contact Email</Label>
                  <Input
                    id="contact_email"
                    name="contact_email"
                    type="email"
                    value={formData.contact_email}
                    onChange={handleChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contact_phone">Contact Phone</Label>
                  <Input
                    id="contact_phone"
                    name="contact_phone"
                    value={formData.contact_phone}
                    onChange={handleChange}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Delivery Settings</CardTitle>
                <CardDescription>
                  Configure delivery options for this store
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="offers_delivery"
                    checked={formData.offers_delivery}
                    onCheckedChange={handleDeliveryChange}
                  />
                  <Label htmlFor="offers_delivery">Store offers delivery</Label>
                </div>

                {formData.offers_delivery && (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="delivery_fee">Delivery Fee (GMD)</Label>
                        <Input
                          id="delivery_fee"
                          name="delivery_fee"
                          type="number"
                          min="0"
                          step="0.01"
                          value={formData.delivery_fee || 0}
                          onChange={(e) => handleNumberChange('delivery_fee', e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="delivery_radius_km">Delivery Radius (km)</Label>
                        <Input
                          id="delivery_radius_km"
                          name="delivery_radius_km"
                          type="number"
                          min="1"
                          value={formData.delivery_radius_km || 10}
                          onChange={(e) => handleNumberChange('delivery_radius_km', e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="delivery_time_estimate">Estimated Delivery Time</Label>
                      <Input
                        id="delivery_time_estimate"
                        name="delivery_time_estimate"
                        value={formData.delivery_time_estimate || ''}
                        onChange={handleChange}
                        placeholder="e.g., 1-2 hours, 30-45 minutes"
                      />
                    </div>
                  </>
                )}

                {!formData.offers_delivery && (
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-sm text-blue-800">
                      When store delivery is disabled, customers will be able to choose from available external delivery services during checkout.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Store Images</CardTitle>
                <CardDescription>
                  Upload logo and cover image for the store
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <ImageUpload
                  label="Store Logo"
                  description="Upload a logo for your store (square format recommended)"
                  currentImageUrl={formData.logo}
                  onImageUploaded={handleLogoUploaded}
                  bucketName="store-images"
                  folderPath="logos"
                  aspectRatio="logo"
                />

                <ImageUpload
                  label="Cover Image"
                  description="Upload a cover image for your store (16:9 format recommended)"
                  currentImageUrl={formData.cover_image}
                  onImageUploaded={handleCoverImageUploaded}
                  bucketName="store-images"
                  folderPath="covers"
                  aspectRatio="cover"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Store Status</CardTitle>
                <CardDescription>
                  Set the store&apos;s visibility and status
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={handleStatusChange}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="disabled">Disabled</SelectItem>
                      <SelectItem value="suspended">Suspended</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2 pt-4">
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={handleFeaturedChange}
                  />
                  <Label htmlFor="featured">Featured Store</Label>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="mt-6 flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/admin/stores')}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Create Store
          </Button>
        </div>
      </form>
    </div>
  );
}
