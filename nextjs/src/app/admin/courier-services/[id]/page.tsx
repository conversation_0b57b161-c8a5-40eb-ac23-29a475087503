'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AdminHeader } from '@/components/admin';
import { ArrowLeft, Edit, Trash2 } from 'lucide-react';
import Link from 'next/link';

interface CourierService {
  id: string;
  name: string;
  description?: string;
  estimated_delivery_time: string;
  base_cost: number;
  cost_per_kg?: number;
  free_weight_limit?: number;
  tracking_supported: boolean;
  countries: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export default function CourierServiceDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;
  
  const [courierService, setCourierService] = useState<CourierService | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCourierService = async () => {
      if (!id) return;
      
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(`/api/admin/courier-services/${id}`);
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to fetch courier service: ${response.status}`);
        }
        const data = await response.json();
        setCourierService(data.courierService);
      } catch (error) {
        console.error('Error fetching courier service:', error);
        setError(error instanceof Error ? error.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchCourierService();
  }, [id]);

  const handleDelete = async () => {
    if (!courierService || !confirm('Are you sure you want to delete this courier service? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/courier-services/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete courier service');
      }

      router.push('/admin/courier-services');
    } catch (error) {
      console.error('Error deleting courier service:', error);
      setError('Failed to delete courier service');
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <AdminHeader
          title="Loading..."
          description="Fetching courier service details"
        />
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !courierService) {
    return (
      <div className="space-y-6">
        <AdminHeader
          title="Error"
          description="Failed to load courier service"
        />
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error || 'Courier service not found'}
        </div>
        <Link href="/admin/courier-services">
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Courier Services
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AdminHeader
        title={courierService.name}
        description="Courier service details and configuration"
      />

      <div className="flex items-center gap-4">
        <Link href="/admin/courier-services">
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Courier Services
          </Button>
        </Link>
        <Link href={`/admin/courier-services/${id}/edit`}>
          <Button>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </Link>
        <Button 
          variant="destructive" 
          onClick={handleDelete}
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Delete
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Information */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Service Information</CardTitle>
              <CardDescription>
                Basic details about the courier service
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900">Name</h4>
                <p className="text-gray-600">{courierService.name}</p>
              </div>
              
              {courierService.description && (
                <div>
                  <h4 className="font-medium text-gray-900">Description</h4>
                  <p className="text-gray-600">{courierService.description}</p>
                </div>
              )}

              <div>
                <h4 className="font-medium text-gray-900">Estimated Delivery Time</h4>
                <p className="text-gray-600">{courierService.estimated_delivery_time}</p>
              </div>

              <div>
                <h4 className="font-medium text-gray-900">Supported Countries</h4>
                <div className="flex flex-wrap gap-2 mt-2">
                  {courierService.countries.map((country) => (
                    <Badge key={country} variant="secondary">
                      {country}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Pricing & Status */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Pricing & Status</CardTitle>
              <CardDescription>
                Cost structure and service status
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900">Base Cost</h4>
                <p className="text-gray-600">${courierService.base_cost.toFixed(2)} USD</p>
              </div>

              {courierService.cost_per_kg && courierService.cost_per_kg > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900">Additional Cost per KG</h4>
                  <p className="text-gray-600">${courierService.cost_per_kg.toFixed(2)} USD</p>
                  {courierService.free_weight_limit && courierService.free_weight_limit > 0 && (
                    <p className="text-sm text-gray-500">
                      (Applied after {courierService.free_weight_limit}kg)
                    </p>
                  )}
                </div>
              )}

              <div>
                <h4 className="font-medium text-gray-900">Features</h4>
                <div className="space-y-2 mt-2">
                  {courierService.tracking_supported && (
                    <Badge variant="secondary">Tracking Supported</Badge>
                  )}
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900">Status</h4>
                <Badge variant={courierService.is_active ? 'default' : 'secondary'}>
                  {courierService.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>

              <div>
                <h4 className="font-medium text-gray-900">Created</h4>
                <p className="text-gray-600">
                  {new Date(courierService.created_at).toLocaleDateString()}
                </p>
              </div>

              <div>
                <h4 className="font-medium text-gray-900">Last Updated</h4>
                <p className="text-gray-600">
                  {new Date(courierService.updated_at).toLocaleDateString()}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}