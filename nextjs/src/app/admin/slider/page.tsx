"use client";
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AdminHeader, DataTable, StatusBadge } from '@/components/admin';
import { Edit, Trash2, Eye, EyeOff, ArrowUp, ArrowDown } from 'lucide-react';
import { getSlides, deleteSlide, updateSlide } from '@/features/admin/api';
import { HeroSlide } from '@/features/admin/types';
import { useToast } from '@/lib/hooks/use-toast';
import Link from 'next/link';
import Image from 'next/image';

export default function SliderManagementPage() {
  const [slides, setSlides] = useState<HeroSlide[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const { toast } = useToast();

  const fetchSlides = async (page = 1) => {
    try {
      setLoading(true);
      const { slides: slidesData, count } = await getSlides({
        page,
        per_page: 10,
        sort_by: 'position',
        sort_order: 'asc'
      });
      setSlides(slidesData);
      setTotalCount(count);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching slides:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch slides',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSlides();
  }, []);

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this slide?')) return;

    try {
      const success = await deleteSlide(id);
      if (success) {
        toast({
          title: 'Success',
          description: 'Slide deleted successfully',
          variant: 'success',
        });
        fetchSlides(currentPage);
      } else {
        throw new Error('Failed to delete slide');
      }
    } catch (error) {
      console.error('Error deleting slide:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete slide',
        variant: 'destructive',
      });
    }
  };

  const handleToggleActive = async (id: string, currentStatus: boolean) => {
    try {
      const success = await updateSlide(id, { is_active: !currentStatus });
      if (success) {
        toast({
          title: 'Success',
          description: `Slide ${!currentStatus ? 'activated' : 'deactivated'} successfully`,
          variant: 'success',
        });
        fetchSlides(currentPage);
      } else {
        throw new Error('Failed to update slide');
      }
    } catch (error) {
      console.error('Error updating slide:', error);
      toast({
        title: 'Error',
        description: 'Failed to update slide',
        variant: 'destructive',
      });
    }
  };

  const handleMoveSlide = async (id: string, direction: 'up' | 'down') => {
    const slideIndex = slides.findIndex(slide => slide.id === id);
    if (slideIndex === -1) return;

    const targetIndex = direction === 'up' ? slideIndex - 1 : slideIndex + 1;
    if (targetIndex < 0 || targetIndex >= slides.length) return;

    const currentSlide = slides[slideIndex];
    const targetSlide = slides[targetIndex];

    try {
      // Swap positions
      await updateSlide(currentSlide.id, { position: targetSlide.position });
      await updateSlide(targetSlide.id, { position: currentSlide.position });

      toast({
        title: 'Success',
        description: 'Slide position updated successfully',
        variant: 'success',
      });
      fetchSlides(currentPage);
    } catch (error) {
      console.error('Error moving slide:', error);
      toast({
        title: 'Error',
        description: 'Failed to update slide position',
        variant: 'destructive',
      });
    }
  };

  const columns = [
    {
      key: 'image_url',
      header: 'Preview',
      cell: (slide: HeroSlide) => (
        <div className="w-16 h-10 relative rounded overflow-hidden">
          <Image
            src={slide.image_url}
            alt={slide.title}
            fill
            className="object-cover"
          />
        </div>
      ),
    },
    {
      key: 'title',
      header: 'Title',
      cell: (slide: HeroSlide) => (
        <div>
          <div className="font-medium">{slide.title}</div>
          {slide.subtitle && (
            <div className="text-sm text-gray-500">{slide.subtitle}</div>
          )}
        </div>
      ),
    },
    {
      key: 'position',
      header: 'Position',
      cell: (slide: HeroSlide) => {
        const slideIndex = slides.findIndex(s => s.id === slide.id);
        return (
          <div className="flex items-center gap-1">
            <span className="font-mono text-sm">{slide.position}</span>
            <div className="flex flex-col gap-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => handleMoveSlide(slide.id, 'up')}
                disabled={slideIndex === 0}
              >
                <ArrowUp className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => handleMoveSlide(slide.id, 'down')}
                disabled={slideIndex === slides.length - 1}
              >
                <ArrowDown className="h-3 w-3" />
              </Button>
            </div>
          </div>
        );
      },
    },
    {
      key: 'is_active',
      header: 'Status',
      cell: (slide: HeroSlide) => (
        <StatusBadge
          status={slide.is_active ? 'active' : 'inactive'}
          variant={slide.is_active ? 'success' : 'secondary'}
        />
      ),
    },
    {
      key: 'link_url',
      header: 'Link',
      cell: (slide: HeroSlide) => (
        <div className="max-w-xs truncate text-sm">
          {slide.link_url || 'No link'}
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (slide: HeroSlide) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleToggleActive(slide.id, slide.is_active)}
          >
            {slide.is_active ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>
          <Link href={`/admin/slider/${slide.id}/edit`}>
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDelete(slide.id)}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Homepage Slider"
        description="Manage the slides displayed on the homepage hero section"
        actionHref="/admin/slider/new"
        actionLabel="Add New Slide"
      />

      <Card>
        <CardHeader>
          <CardTitle>Slides ({totalCount})</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={slides}
            totalCount={totalCount}
            pageSize={10}
            currentPage={currentPage}
            onPageChange={fetchSlides}
            isLoading={loading}
            getRowKey={(slide) => slide.id}
          />
        </CardContent>
      </Card>
    </div>
  );
}
