"use client";
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { AdminHeader } from '@/components/admin';
import { getSlide, updateSlide } from '@/features/admin/api';
import { HeroSlide, UpdateSlideParams } from '@/features/admin/types';
import { useToast } from '@/lib/hooks/use-toast';
import { useRouter, useParams } from 'next/navigation';
import Image from 'next/image';

export default function EditSlidePage() {
  const [slide, setSlide] = useState<HeroSlide | null>(null);
  const [formData, setFormData] = useState<UpdateSlideParams>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [imagePreview, setImagePreview] = useState('');
  const { toast } = useToast();
  const router = useRouter();
  const params = useParams();
  const slideId = params.id as string;

  useEffect(() => {
    const fetchSlide = async () => {
      try {
        const slideData = await getSlide(slideId);
        if (slideData) {
          setSlide(slideData);
          setFormData({
            title: slideData.title,
            subtitle: slideData.subtitle,
            image_url: slideData.image_url,
            link_url: slideData.link_url,
            link_text: slideData.link_text,
            background_color: slideData.background_color,
            text_color: slideData.text_color,
            position: slideData.position,
            is_active: slideData.is_active,
          });
          setImagePreview(slideData.image_url);
        } else {
          toast({
            title: 'Error',
            description: 'Slide not found',
            variant: 'destructive',
          });
          router.push('/admin/slider');
        }
      } catch (error) {
        console.error('Error fetching slide:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch slide',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    if (slideId) {
      fetchSlide();
    }
  }, [slideId, router, toast]);

  const handleInputChange = (field: keyof UpdateSlideParams, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Update image preview when image URL changes
    if (field === 'image_url') {
      setImagePreview(value);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.image_url) {
      toast({
        title: 'Error',
        description: 'Title and image URL are required',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSaving(true);
      const updatedSlide = await updateSlide(slideId, formData);
      
      if (updatedSlide) {
        toast({
          title: 'Success',
          description: 'Slide updated successfully',
          variant: 'success',
        });
        router.push('/admin/slider');
      } else {
        throw new Error('Failed to update slide');
      }
    } catch (error) {
      console.error('Error updating slide:', error);
      toast({
        title: 'Error',
        description: 'Failed to update slide',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!slide) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Slide not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Edit Slide"
        description={`Editing: ${slide.title}`}
        backHref="/admin/slider"
        backLabel="Back to Slides"
      />

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Slide Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={formData.title || ''}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter slide title"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="position">Position</Label>
                <Input
                  id="position"
                  type="number"
                  min="1"
                  value={formData.position || 1}
                  onChange={(e) => handleInputChange('position', parseInt(e.target.value))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="subtitle">Subtitle</Label>
              <Input
                id="subtitle"
                value={formData.subtitle || ''}
                onChange={(e) => handleInputChange('subtitle', e.target.value)}
                placeholder="Enter slide subtitle (optional)"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="image_url">Image URL *</Label>
              <Input
                id="image_url"
                value={formData.image_url || ''}
                onChange={(e) => handleInputChange('image_url', e.target.value)}
                placeholder="https://example.com/image.jpg"
                required
              />
              {imagePreview && (
                <div className="mt-2">
                  <div className="relative w-full h-32 rounded-lg overflow-hidden border">
                    <Image
                      src={imagePreview}
                      alt="Preview"
                      fill
                      className="object-cover"
                      onError={() => setImagePreview('')}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Link Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="link_url">Link URL</Label>
                <Input
                  id="link_url"
                  value={formData.link_url || ''}
                  onChange={(e) => handleInputChange('link_url', e.target.value)}
                  placeholder="/categories/electronics"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="link_text">Link Text</Label>
                <Input
                  id="link_text"
                  value={formData.link_text || ''}
                  onChange={(e) => handleInputChange('link_text', e.target.value)}
                  placeholder="Learn More"
                />
              </div>
            </div>

            {/* Styling */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="background_color">Background Color</Label>
                <div className="flex gap-2">
                  <Input
                    id="background_color"
                    type="color"
                    value={formData.background_color || '#F78100'}
                    onChange={(e) => handleInputChange('background_color', e.target.value)}
                    className="w-16 h-10 p-1"
                  />
                  <Input
                    value={formData.background_color || '#F78100'}
                    onChange={(e) => handleInputChange('background_color', e.target.value)}
                    placeholder="#F78100"
                    className="flex-1"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="text_color">Text Color</Label>
                <div className="flex gap-2">
                  <Input
                    id="text_color"
                    type="color"
                    value={formData.text_color || '#FFFFFF'}
                    onChange={(e) => handleInputChange('text_color', e.target.value)}
                    className="w-16 h-10 p-1"
                  />
                  <Input
                    value={formData.text_color || '#FFFFFF'}
                    onChange={(e) => handleInputChange('text_color', e.target.value)}
                    placeholder="#FFFFFF"
                    className="flex-1"
                  />
                </div>
              </div>
            </div>

            {/* Status */}
            <div className="flex items-center space-x-2">
              <Switch
                id="is_active"
                checked={formData.is_active || false}
                onCheckedChange={(checked) => handleInputChange('is_active', checked)}
              />
              <Label htmlFor="is_active">Active</Label>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-4 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push('/admin/slider')}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={saving}>
                {saving ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}
