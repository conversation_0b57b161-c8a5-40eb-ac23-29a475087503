"use client";
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AdminHeader, ImageUpload } from '@/components/admin';
import { createCategory, getCategoriesSimple } from '@/features/admin/api';
import { CreateCategoryParams, Category } from '@/features/admin/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertCircle } from 'lucide-react';
import { slugify } from '@/lib/utils/string';
import { useToast } from '@/lib/hooks/use-toast';

export default function NewCategoryPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [parentCategories, setParentCategories] = useState<Category[]>([]);

  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<CreateCategoryParams>({
    name: '',
    slug: '',
    description: '',
    image: '',
    thumbnail: '',
    parent_id: '',
    featured: false,
  });

  useEffect(() => {
    const fetchParentCategories = async () => {
      try {
        const categories = await getCategoriesSimple();
        setParentCategories(categories);
      } catch (error) {
        console.error('Error fetching parent categories:', error);
      }
    };

    fetchParentCategories();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Auto-generate slug when name changes
    if (name === 'name') {
      setFormData((prev) => ({ ...prev, slug: slugify(value) }));
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value === 'none' ? null : value
    }));
  };

  const handleFeaturedChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, featured: checked }));
  };

  const handleImageUploaded = (url: string) => {
    setFormData((prev) => ({ ...prev, image: url }));
  };

  const handleThumbnailUploaded = (url: string) => {
    setFormData((prev) => ({ ...prev, thumbnail: url }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      const newCategory = await createCategory(formData);
      if (newCategory) {
        toast({
          title: "Success",
          description: "Category created successfully",
          variant: "default",
        });
        router.push('/admin/categories');
        router.refresh();
      } else {
        const errorMessage = 'Failed to create category';
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (err) {
      console.error('Error creating category:', err);
      const errorMessage = 'An error occurred while creating the category';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Add New Category"
        description="Create a new product category"
        backHref="/admin/categories"
      />

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Category Information</CardTitle>
                <CardDescription>
                  Enter the category&apos;s basic information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Category Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">Category Slug</Label>
                  <Input
                    id="slug"
                    name="slug"
                    value={formData.slug}
                    onChange={handleChange}
                    required
                  />
                  <p className="text-sm text-gray-500">
                    This will be used in the category&apos;s URL: /categories/{formData.slug}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={4}
                    placeholder="Optional description for the category"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="parent_id">Parent Category</Label>
                  <Select
                    value={formData.parent_id || undefined}
                    onValueChange={(value) => handleSelectChange('parent_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a parent category (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None (Top Level)</SelectItem>
                      {parentCategories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Category Images</CardTitle>
                <CardDescription>
                  Upload images for the category
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <ImageUpload
                  label="Category Image"
                  description="Upload a main image for the category (recommended: 800x600px)"
                  currentImageUrl={formData.image}
                  onImageUploaded={handleImageUploaded}
                  bucketName="category-images"
                  folderPath="main"
                  aspectRatio="cover"
                />

                <ImageUpload
                  label="Category Thumbnail"
                  description="Upload a thumbnail for the category (recommended: 200x200px)"
                  currentImageUrl={formData.thumbnail}
                  onImageUploaded={handleThumbnailUploaded}
                  bucketName="category-images"
                  folderPath="thumbnails"
                  aspectRatio="square"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Category Settings</CardTitle>
                <CardDescription>
                  Configure category visibility and features
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between space-x-2">
                  <div className="space-y-0.5">
                    <Label htmlFor="featured">Featured Category</Label>
                    <div className="text-sm text-gray-500">
                      Show this category prominently on the homepage
                    </div>
                  </div>
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={handleFeaturedChange}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="mt-6 flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/admin/categories')}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Create Category
          </Button>
        </div>
      </form>
    </div>
  );
}
