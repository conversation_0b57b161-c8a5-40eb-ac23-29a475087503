"use client";
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AdminHeader, ImageUpload } from '@/components/admin';
import { getCategory, updateCategory, getCategoriesSimple } from '@/features/admin/api';
import { Category, UpdateCategoryParams } from '@/features/admin/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertCircle } from 'lucide-react';
import { slugify } from '@/lib/utils/string';

export default function EditCategoryPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const [category, setCategory] = useState<Category | null>(null);
  const [parentCategories, setParentCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [categoryId, setCategoryId] = useState<string | null>(null);
  const [formData, setFormData] = useState<UpdateCategoryParams>({
    name: '',
    slug: '',
    description: '',
    image: '',
    thumbnail: '',
    parent_id: '',
    featured: false,
  });

  // Unwrap params using React.use()
  useEffect(() => {
    const unwrapParams = async () => {
      const resolvedParams = await params;
      setCategoryId(resolvedParams.id);
    };
    unwrapParams();
  }, [params]);

  useEffect(() => {
    if (!categoryId) return;

    const fetchData = async () => {
      setLoading(true);
      try {
        const [categoryData, parentCategoriesData] = await Promise.all([
          getCategory(categoryId),
          getCategoriesSimple()
        ]);

        if (categoryData) {
          setCategory(categoryData);
          setFormData({
            name: categoryData.name,
            slug: categoryData.slug,
            description: categoryData.description || '',
            image: categoryData.image || '',
            thumbnail: categoryData.thumbnail || '',
            parent_id: categoryData.parent_id || '',
            featured: categoryData.featured,
          });
        }

        // Filter out the current category from parent options to prevent circular references
        const filteredParents = parentCategoriesData.filter(cat => cat.id !== categoryId);
        setParentCategories(filteredParents);
      } catch (error) {
        console.error('Error fetching category:', error);
        setError('Failed to load category data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [categoryId]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Auto-generate slug when name changes
    if (name === 'name') {
      setFormData((prev) => ({ ...prev, slug: slugify(value) }));
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value === 'none' ? null : value
    }));
  };

  const handleFeaturedChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, featured: checked }));
  };

  const handleImageUploaded = (url: string) => {
    setFormData((prev) => ({ ...prev, image: url }));
  };

  const handleThumbnailUploaded = (url: string) => {
    setFormData((prev) => ({ ...prev, thumbnail: url }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!categoryId) return;

    setSaving(true);
    setError(null);

    try {
      const updatedCategory = await updateCategory(categoryId, formData);
      if (updatedCategory) {
        router.push('/admin/categories');
        router.refresh();
      } else {
        setError('Failed to update category');
      }
    } catch (error) {
      console.error('Error updating category:', error);
      setError('An error occurred while updating the category');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!category) {
    return (
      <div className="space-y-6">
        <AdminHeader
          title="Category Not Found"
          description="The requested category could not be found"
          backHref="/admin/categories"
        />
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            The category you are trying to edit does not exist or you don&apos;t have permission to view it.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Edit Category"
        description="Update category information and settings"
        backHref="/admin/categories"
      />

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Category Information</CardTitle>
                <CardDescription>
                  Update the category&apos;s basic information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Category Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">Category Slug</Label>
                  <Input
                    id="slug"
                    name="slug"
                    value={formData.slug}
                    onChange={handleChange}
                    required
                  />
                  <p className="text-sm text-gray-500">
                    This will be used in the category&apos;s URL: /categories/{formData.slug}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={4}
                    placeholder="Optional description for the category"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="parent_id">Parent Category</Label>
                  <Select
                    value={formData.parent_id || undefined}
                    onValueChange={(value) => handleSelectChange('parent_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a parent category (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None (Top Level)</SelectItem>
                      {parentCategories.map((parentCategory) => (
                        <SelectItem key={parentCategory.id} value={parentCategory.id}>
                          {parentCategory.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Category Images</CardTitle>
                <CardDescription>
                  Upload images for the category
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <ImageUpload
                  label="Category Image"
                  description="Upload a main image for the category (recommended: 800x600px)"
                  currentImageUrl={formData.image}
                  onImageUploaded={handleImageUploaded}
                  bucketName="category-images"
                  folderPath="main"
                  aspectRatio="cover"
                />

                <ImageUpload
                  label="Category Thumbnail"
                  description="Upload a thumbnail for the category (recommended: 200x200px)"
                  currentImageUrl={formData.thumbnail}
                  onImageUploaded={handleThumbnailUploaded}
                  bucketName="category-images"
                  folderPath="thumbnails"
                  aspectRatio="square"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Category Settings</CardTitle>
                <CardDescription>
                  Configure category visibility and features
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between space-x-2">
                  <div className="space-y-0.5">
                    <Label htmlFor="featured">Featured Category</Label>
                    <div className="text-sm text-gray-500">
                      Show this category prominently on the homepage
                    </div>
                  </div>
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={handleFeaturedChange}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Category Stats</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium">Products: </span>
                    <span>{category.product_count || 0}</span>
                  </div>
                  <div>
                    <span className="font-medium">Created: </span>
                    <span>{new Date(category.created_at).toLocaleDateString()}</span>
                  </div>
                  <div>
                    <span className="font-medium">Last Updated: </span>
                    <span>{new Date(category.updated_at).toLocaleDateString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="mt-6 flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/admin/categories')}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Save Changes
          </Button>
        </div>
      </form>
    </div>
  );
}
