"use client";
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { AdminHeader } from '@/components/admin';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CreateDeliveryServiceParams } from '@/features/admin/types';
import { Truck, ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react';
import Link from 'next/link';

export default function NewDeliveryServicePage() {
  const router = useRouter();
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState<CreateDeliveryServiceParams>({
    name: '',
    description: '',
    logo: '',
    contact_phone: '',
    contact_email: '',
    website: '',
    base_fee: 0,
    per_km_fee: 0,
    minimum_order_amount: 0,
    maximum_delivery_radius_km: 20,
    estimated_delivery_time: '30-60 minutes',
    is_active: true,
    coverage_areas: [],
    operating_hours: {},
  });

  const [coverageAreasText, setCoverageAreasText] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleNumberChange = (field: string, value: string) => {
    const numValue = parseFloat(value) || 0;
    setFormData((prev) => ({ ...prev, [field]: numValue }));
  };

  const handleActiveChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, is_active: checked }));
  };

  const handleCoverageAreasChange = (value: string) => {
    setCoverageAreasText(value);
    const areas = value.split(',').map(area => area.trim()).filter(area => area.length > 0);
    setFormData((prev) => ({ ...prev, coverage_areas: areas }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/admin/delivery-services', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create delivery service');
      }

      setSuccess('Delivery service created successfully!');
      setTimeout(() => {
        router.push('/admin/delivery-services');
      }, 2000);
    } catch (error) {
      console.error('Error creating delivery service:', error);
      setError(error instanceof Error ? error.message : 'Failed to create delivery service');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/admin/delivery-services">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Delivery Services
          </Button>
        </Link>
        <AdminHeader
          title="Add New Delivery Service"
          description="Create a new external delivery service provider"
          icon={Truck}
        />
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>
                  Basic details about the delivery service
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Service Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Enter service name"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Describe the delivery service"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="logo">Logo URL</Label>
                  <Input
                    id="logo"
                    name="logo"
                    type="url"
                    value={formData.logo}
                    onChange={handleChange}
                    placeholder="https://example.com/logo.png"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_active"
                    checked={formData.is_active}
                    onCheckedChange={handleActiveChange}
                  />
                  <Label htmlFor="is_active">Active</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
                <CardDescription>
                  How to reach the delivery service
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="contact_phone">Phone Number</Label>
                  <Input
                    id="contact_phone"
                    name="contact_phone"
                    value={formData.contact_phone}
                    onChange={handleChange}
                    placeholder="+************"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contact_email">Email Address</Label>
                  <Input
                    id="contact_email"
                    name="contact_email"
                    type="email"
                    value={formData.contact_email}
                    onChange={handleChange}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    name="website"
                    type="url"
                    value={formData.website}
                    onChange={handleChange}
                    placeholder="https://deliveryservice.com"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Pricing and Service Details */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Pricing</CardTitle>
                <CardDescription>
                  Set the delivery fees and pricing structure
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="base_fee">Base Fee (GMD) *</Label>
                  <Input
                    id="base_fee"
                    name="base_fee"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.base_fee}
                    onChange={(e) => handleNumberChange('base_fee', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="per_km_fee">Per Kilometer Fee (GMD)</Label>
                  <Input
                    id="per_km_fee"
                    name="per_km_fee"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.per_km_fee}
                    onChange={(e) => handleNumberChange('per_km_fee', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="minimum_order_amount">Minimum Order Amount (GMD)</Label>
                  <Input
                    id="minimum_order_amount"
                    name="minimum_order_amount"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.minimum_order_amount}
                    onChange={(e) => handleNumberChange('minimum_order_amount', e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Service Details</CardTitle>
                <CardDescription>
                  Coverage area and delivery specifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="maximum_delivery_radius_km">Maximum Delivery Radius (km)</Label>
                  <Input
                    id="maximum_delivery_radius_km"
                    name="maximum_delivery_radius_km"
                    type="number"
                    min="1"
                    value={formData.maximum_delivery_radius_km}
                    onChange={(e) => handleNumberChange('maximum_delivery_radius_km', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="estimated_delivery_time">Estimated Delivery Time</Label>
                  <Input
                    id="estimated_delivery_time"
                    name="estimated_delivery_time"
                    value={formData.estimated_delivery_time}
                    onChange={handleChange}
                    placeholder="e.g., 30-60 minutes"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="coverage_areas">Coverage Areas</Label>
                  <Textarea
                    id="coverage_areas"
                    value={coverageAreasText}
                    onChange={(e) => handleCoverageAreasChange(e.target.value)}
                    placeholder="Enter areas separated by commas (e.g., Banjul, Serrekunda, Bakau)"
                    rows={3}
                  />
                  <p className="text-sm text-gray-500">
                    Separate multiple areas with commas
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Link href="/admin/delivery-services">
            <Button type="button" variant="outline">
              Cancel
            </Button>
          </Link>
          <Button type="submit" disabled={saving}>
            {saving ? 'Creating...' : 'Create Delivery Service'}
          </Button>
        </div>
      </form>
    </div>
  );
}
