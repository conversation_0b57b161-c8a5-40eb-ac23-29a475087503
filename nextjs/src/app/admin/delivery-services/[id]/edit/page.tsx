"use client";

import React, { useEffect, useState, FormEvent } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { AdminHeader } from '@/components/admin';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ExternalDeliveryService } from '@/features/admin/types';
import { ArrowLeft, Save } from 'lucide-react';
import { useToast } from '@/lib/hooks/use-toast';

// Define a type for the form data, excluding read-only fields like id, created_at, updated_at
type DeliveryServiceFormData = Omit<ExternalDeliveryService, 'id' | 'created_at' | 'updated_at'>;

export default function EditDeliveryServicePage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const id = params.id as string;

  const [formData, setFormData] = useState<Partial<DeliveryServiceFormData>>({});
  const [initialService, setInitialService] = useState<ExternalDeliveryService | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchServiceDetails = async () => {
        setLoading(true);
        setError(null);
        try {
          const response = await fetch(`/api/admin/delivery-services/${id}`);
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Failed to fetch service: ${response.statusText}`);
          }
          const data = await response.json();
          if (data.service) {
            setInitialService(data.service);
            // Exclude read-only fields for the form
            const { id, created_at, updated_at, ...editableServiceData } = data.service;
            setFormData({
              ...editableServiceData,
              // Ensure operating_hours and coverage_areas are stringified for Textarea
              operating_hours: data.service.operating_hours ? JSON.stringify(data.service.operating_hours, null, 2) : '',
              coverage_areas: data.service.coverage_areas ? JSON.stringify(data.service.coverage_areas, null, 2) : '',
            });
          } else {
            throw new Error('Service data not found in response.');
          }
        } catch (err: any) {
          console.error("Error fetching delivery service details for edit:", err);
          setError(err.message || 'An unknown error occurred.');
        } finally {
          setLoading(false);
        }
      };
      fetchServiceDetails();
    }
  }, [id]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleSwitchChange = (checked: boolean, name: string) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };


  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    let processedFormData = { ...formData };

    // Attempt to parse JSON fields
    try {
      if (formData.operating_hours && typeof formData.operating_hours === 'string') {
        processedFormData.operating_hours = JSON.parse(formData.operating_hours);
      }
      if (formData.coverage_areas && typeof formData.coverage_areas === 'string') {
        processedFormData.coverage_areas = JSON.parse(formData.coverage_areas);
      }
    } catch (jsonError: any) {
      setError(`Invalid JSON format for Operating Hours or Coverage Areas: ${jsonError.message}`);
      setSaving(false);
      toast({ title: "Error", description: `Invalid JSON format.`, variant: "destructive" });
      return;
    }
    
    // Convert numeric fields from string to number
    const numericFields: (keyof DeliveryServiceFormData)[] = ['base_fee', 'per_km_fee', 'minimum_order_amount', 'maximum_delivery_radius_km'];
    numericFields.forEach(field => {
      if (processedFormData[field] !== null && processedFormData[field] !== undefined && processedFormData[field] !== '') {
        const numValue = parseFloat(processedFormData[field] as string);
        (processedFormData as any)[field] = isNaN(numValue) ? null : numValue;
      } else if (processedFormData[field] === '') {
         (processedFormData as any)[field] = null; // Treat empty string as null for optional numeric fields
      }
    });


    try {
      const response = await fetch(`/api/admin/delivery-services/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(processedFormData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update service: ${response.statusText}`);
      }
      
      toast({ title: "Success", description: "Delivery service updated successfully." });
      router.push(`/admin/delivery-services/${id}`); // Redirect to details page
    } catch (err: any) {
      console.error("Error updating delivery service:", err);
      setError(err.message || 'An unknown error occurred while saving.');
      toast({ title: "Error", description: err.message || "Failed to save.", variant: "destructive" });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <div className="flex justify-center items-center h-screen"><div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div></div>;
  }

  if (error && !initialService) { // Show full page error if initial load failed
    return (
      <div className="space-y-6 p-4 md:p-6">
        <AdminHeader title="Error" description="Could not load service for editing." />
        <Alert variant="destructive"><AlertDescription>{error}</AlertDescription></Alert>
        <Button onClick={() => router.push('/admin/delivery-services')}><ArrowLeft className="mr-2 h-4 w-4" /> Back to List</Button>
      </div>
    );
  }
  
  const formFields: { name: keyof DeliveryServiceFormData; label: string; type: string; component?: 'textarea' | 'switch' }[] = [
    { name: 'name', label: 'Service Name', type: 'text' },
    { name: 'description', label: 'Description', type: 'text', component: 'textarea' },
    { name: 'logo', label: 'Logo URL', type: 'text' },
    { name: 'contact_phone', label: 'Contact Phone', type: 'text' },
    { name: 'contact_email', label: 'Contact Email', type: 'email' },
    { name: 'website', label: 'Website URL', type: 'text' },
    { name: 'base_fee', label: 'Base Fee (GMD)', type: 'number' },
    { name: 'per_km_fee', label: 'Per KM Fee (GMD)', type: 'number' },
    { name: 'minimum_order_amount', label: 'Minimum Order Amount (GMD)', type: 'number' },
    { name: 'maximum_delivery_radius_km', label: 'Max Delivery Radius (KM)', type: 'number' },
    { name: 'estimated_delivery_time', label: 'Estimated Delivery Time (e.g., 30-45 mins)', type: 'text' },
    { name: 'is_active', label: 'Is Active', type: 'boolean', component: 'switch' },
    { name: 'coverage_areas', label: 'Coverage Areas (JSON Array)', type: 'text', component: 'textarea' },
    { name: 'operating_hours', label: 'Operating Hours (JSON Object)', type: 'text', component: 'textarea' },
  ];


  return (
    <div className="space-y-6 p-4 md:p-6">
      <AdminHeader
        title={initialService?.name ? `Edit: ${initialService.name}` : 'Edit Delivery Service'}
        description="Modify the details of the delivery service."
        backHref={`/admin/delivery-services/${id}`}
        backLabel="Back to Details"
      />

      {error && ( // Display non-critical errors (e.g., save error) here
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Service Information</CardTitle>
            <CardDescription>Update the fields below and click save.</CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {formFields.map(field => (
              <div key={field.name} className={field.component === 'textarea' ? 'md:col-span-2' : ''}>
                <Label htmlFor={field.name} className="text-sm font-medium">
                  {field.label}
                </Label>
                {field.component === 'textarea' ? (
                  <Textarea
                    id={field.name}
                    name={field.name}
                    value={(formData[field.name] as string) || ''}
                    onChange={handleChange}
                    rows={field.name === 'operating_hours' || field.name === 'coverage_areas' ? 6 : 3}
                    className="mt-1"
                  />
                ) : field.component === 'switch' ? (
                  <div className="mt-2">
                    <Switch
                      id={field.name}
                      name={field.name}
                      checked={formData[field.name] as boolean || false}
                      onCheckedChange={(checked) => handleSwitchChange(checked, field.name)}
                    />
                  </div>
                ) : (
                  <Input
                    id={field.name}
                    name={field.name}
                    type={field.type}
                    value={(formData[field.name] as string | number) || ''}
                    onChange={handleChange}
                    className="mt-1"
                    step={field.type === 'number' ? '0.01' : undefined}
                  />
                )}
              </div>
            ))}
          </CardContent>
        </Card>
        <div className="mt-6 flex justify-end gap-3">
          <Button type="button" variant="outline" onClick={() => router.push(`/admin/delivery-services/${id}`)} disabled={saving}>
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            <Save className="mr-2 h-4 w-4" /> {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </form>
    </div>
  );
}