"use client";

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { AdminHeader } from '@/components/admin';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ExternalDeliveryService } from '@/features/admin/types'; // Assuming this type exists and is correct
import { ArrowLeft, Edit, Truck, DollarSign, Clock, MapPin, Phone, Mail, Globe } from 'lucide-react';
import Link from 'next/link';
import { formatCurrency } from '@/utils';

export default function DeliveryServiceDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;

  const [service, setService] = useState<ExternalDeliveryService | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchServiceDetails = async () => {
        setLoading(true);
        setError(null);
        try {
          const response = await fetch(`/api/admin/delivery-services/${id}`);
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Failed to fetch service: ${response.statusText}`);
          }
          const data = await response.json();
          setService(data.service);
        } catch (err: any) {
          console.error("Error fetching delivery service details:", err);
          setError(err.message || 'An unknown error occurred.');
        } finally {
          setLoading(false);
        }
      };
      fetchServiceDetails();
    }
  }, [id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6 p-4 md:p-6">
        <AdminHeader title="Error" description="Could not load delivery service details." />
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button onClick={() => router.push('/admin/delivery-services')}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to List
        </Button>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="space-y-6 p-4 md:p-6">
        <AdminHeader title="Not Found" description="Delivery service not found." />
        <Alert variant="default">
          <AlertDescription>The requested delivery service could not be found.</AlertDescription>
        </Alert>
        <Button onClick={() => router.push('/admin/delivery-services')}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to List
        </Button>
      </div>
    );
  }

  // Helper to display a value or a placeholder
  const displayValue = (value: string | number | null | undefined, placeholder: string = 'N/A') => {
    return value !== null && value !== undefined && value !== '' ? value : placeholder;
  };

  return (
    <div className="space-y-6 p-4 md:p-6">
      <AdminHeader
        title={service.name || 'Delivery Service Details'}
        description={`Details for ${service.name}`}
        backHref="/admin/delivery-services"
      />

      <div className="flex justify-end gap-2">
        <Link href={`/admin/delivery-services/${service.id}/edit`}>
          <Button variant="outline">
            <Edit className="mr-2 h-4 w-4" /> Edit Service
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            {service.logo ? (
              <img src={service.logo} alt={service.name} className="w-16 h-16 rounded-lg object-cover border" />
            ) : (
              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center border">
                <Truck className="h-8 w-8 text-gray-400" />
              </div>
            )}
            <div>
              <CardTitle className="text-2xl">{service.name}</CardTitle>
              <CardDescription>{displayValue(service.description)}</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="grid gap-6 md:grid-cols-2">
          {/* Contact Information */}
          <div className="space-y-3">
            <h3 className="text-md font-semibold text-gray-700">Contact Information</h3>
            <div className="text-sm space-y-1">
              <p><Phone className="inline mr-2 h-4 w-4 text-gray-500" /> Phone: {displayValue(service.contact_phone)}</p>
              <p><Mail className="inline mr-2 h-4 w-4 text-gray-500" /> Email: {displayValue(service.contact_email)}</p>
              <p><Globe className="inline mr-2 h-4 w-4 text-gray-500" /> Website: 
                {service.website ? 
                  <a href={service.website} target="_blank" rel="noopener noreferrer" className="text-primary-600 hover:underline ml-1">
                    {service.website}
                  </a> : 'N/A'}
              </p>
            </div>
          </div>

          {/* Pricing Details */}
          <div className="space-y-3">
            <h3 className="text-md font-semibold text-gray-700">Pricing</h3>
            <div className="text-sm space-y-1">
              <p><DollarSign className="inline mr-2 h-4 w-4 text-gray-500" /> Base Fee: {formatCurrency(service.base_fee || 0, 'GMD')}</p>
              <p><DollarSign className="inline mr-2 h-4 w-4 text-gray-500" /> Per KM Fee: {service.per_km_fee ? formatCurrency(service.per_km_fee, 'GMD') : 'N/A'}</p>
              <p><DollarSign className="inline mr-2 h-4 w-4 text-gray-500" /> Minimum Order Amount: {service.minimum_order_amount ? formatCurrency(service.minimum_order_amount, 'GMD') : 'N/A'}</p>
            </div>
          </div>
          
          {/* Service Details */}
          <div className="space-y-3">
            <h3 className="text-md font-semibold text-gray-700">Service Details</h3>
            <div className="text-sm space-y-1">
              <p><MapPin className="inline mr-2 h-4 w-4 text-gray-500" /> Max Delivery Radius: {displayValue(service.maximum_delivery_radius_km, 'N/A')} km</p>
              <p><Clock className="inline mr-2 h-4 w-4 text-gray-500" /> Estimated Delivery Time: {displayValue(service.estimated_delivery_time)}</p>
              <p>Status: 
                <span className={`ml-2 px-2 py-0.5 rounded-full text-xs font-medium ${service.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                  {service.is_active ? 'Active' : 'Inactive'}
                </span>
              </p>
            </div>
          </div>

          {/* Coverage Areas */}
          {service.coverage_areas && service.coverage_areas.length > 0 && (
            <div className="space-y-3 md:col-span-2">
              <h3 className="text-md font-semibold text-gray-700">Coverage Areas</h3>
              <div className="flex flex-wrap gap-2">
                {(service.coverage_areas as string[]).map((area, index) => ( // Assuming string array for now
                  <span key={index} className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                    {area}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Operating Hours - Needs careful display based on JSON structure */}
          {service.operating_hours && typeof service.operating_hours === 'object' && Object.keys(service.operating_hours).length > 0 && (
            <div className="space-y-3 md:col-span-2">
              <h3 className="text-md font-semibold text-gray-700">Operating Hours</h3>
              <div className="text-sm space-y-1 bg-gray-50 p-3 rounded-md">
                {Object.entries(service.operating_hours).map(([day, hours]) => (
                  <p key={day} className="capitalize">
                    <span className="font-medium">{day.replace(/_/g, ' ')}:</span> {String(hours)}
                  </p>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}