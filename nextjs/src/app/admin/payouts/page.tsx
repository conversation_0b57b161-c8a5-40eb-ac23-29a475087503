"use client";
import React, { useState, useEffect } from 'react';
import { AdminHeader, DataTable, StatusBadge } from '@/components/admin';
import PayoutDetailsModal from '@/components/admin/PayoutDetailsModal';
import { getPayouts } from '@/features/admin/api';
import { AdminPayout, PayoutListParams } from '@/features/admin/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Receipt } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { formatCurrency } from '@/lib/utils/format';

export default function PayoutsPage() {
  const [payouts, setPayouts] = useState<AdminPayout[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [selectedPayout, setSelectedPayout] = useState<AdminPayout | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [params, setParams] = useState<PayoutListParams>({
    page: 1,
    per_page: 10,
    sort_by: 'created_at',
    sort_order: 'desc',
  });

  useEffect(() => {
    const fetchPayouts = async () => {
      setLoading(true);
      try {
        const { payouts, count } = await getPayouts(params);
        setPayouts(payouts);
        setTotalCount(count);
      } catch (error) {
        console.error('Error fetching payouts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPayouts();
  }, [params]);

  const handlePageChange = (page: number) => {
    setParams({ ...params, page });
  };

  const handleSearch = (search: string) => {
    setParams({ ...params, page: 1, search });
  };

  const handleSortChange = (sort_by: string, sort_order: 'asc' | 'desc') => {
    setParams({ ...params, sort_by, sort_order });
  };

  const handleStatusFilter = (payout_status: string) => {
    setParams({
      ...params,
      page: 1,
      payout_status: payout_status === 'all' ? undefined : payout_status,
    });
  };

  const handleViewPayout = (payout: AdminPayout) => {
    setSelectedPayout(payout);
    setIsModalOpen(true);
  };

  const handlePayoutUpdated = () => {
    // Refresh the payouts list
    const fetchPayouts = async () => {
      try {
        const { payouts, count } = await getPayouts(params);
        setPayouts(payouts);
        setTotalCount(count);
      } catch (error) {
        console.error('Error refreshing payouts:', error);
      }
    };
    fetchPayouts();
  };

  const columns = [
    {
      key: 'id',
      header: 'Payout ID',
      cell: (payout: AdminPayout) => (
        <div className="font-medium text-primary-600">
          {payout.id.substring(0, 8)}...
        </div>
      ),
      sortable: true,
    },
    {
      key: 'store_name',
      header: 'Store',
      cell: (payout: AdminPayout) => (
        <Link href={`/admin/stores/${payout.store_id}`} className="text-primary-600 hover:underline">
          {payout.store_name || payout.store_id.substring(0, 8)}
        </Link>
      ),
      sortable: true,
    },
    {
      key: 'order_id',
      header: 'Order ID',
      cell: (payout: AdminPayout) => (
        <Link href={`/admin/orders/${payout.order_id}`} className="text-primary-600 hover:underline">
          {payout.order_id.substring(0, 8)}...
        </Link>
      ),
      sortable: true,
    },
    {
      key: 'amount',
      header: 'Amount',
      cell: (payout: AdminPayout) => (
        <div className="font-medium">
          {formatCurrency(payout.amount, payout.currency)}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'commission',
      header: 'Commission',
      cell: (payout: AdminPayout) => (
        <div className="text-gray-500">
          {formatCurrency(payout.commission, payout.currency)}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'payout_status',
      header: 'Status',
      cell: (payout: AdminPayout) => <StatusBadge status={payout.payout_status} />,
      sortable: true,
    },
    {
      key: 'transaction_id',
      header: 'Transaction ID',
      cell: (payout: AdminPayout) => (
        <div className="text-sm text-gray-500">
          {payout.transaction_id ? payout.transaction_id : '-'}
        </div>
      ),
    },
    {
      key: 'created_at',
      header: 'Date',
      cell: (payout: AdminPayout) => (
        <div>{new Date(payout.created_at).toLocaleDateString()}</div>
      ),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (payout: AdminPayout) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewPayout(payout)}
          >
            <Receipt className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Payouts"
        description="Manage your marketplace payouts to stores"
      />

      {/* Filters */}
      <div className="flex flex-wrap gap-4">
        <div className="w-full sm:w-auto">
          <Select
            value={params.payout_status || 'all'}
            onValueChange={handleStatusFilter}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Payout Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <DataTable
        columns={columns}
        data={payouts}
        totalCount={totalCount}
        pageSize={params.per_page || 10}
        currentPage={params.page || 1}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
        searchPlaceholder="Search payouts..."
        onSortChange={handleSortChange}
        sortKey={params.sort_by}
        sortOrder={params.sort_order}
        isLoading={loading}
        getRowKey={(payout) => payout.id}
      />

      {/* Payout Details Modal */}
      <PayoutDetailsModal
        payout={selectedPayout}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onPayoutUpdated={handlePayoutUpdated}
      />
    </div>
  );
}
