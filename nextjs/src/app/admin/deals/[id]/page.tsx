'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Edit, Trash2, Calendar, Package, Clock } from 'lucide-react';
import { AdminHeader, StatusBadge } from '@/components/admin';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { getDeal, deleteDeal } from '@/features/admin/api';
import { AdminDeal } from '@/features/admin/types';
import { formatCurrency } from '@/utils';
import { useToast } from '@/lib/hooks/use-toast';

export default function DealDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const { toast } = useToast();
  const [deal, setDeal] = useState<AdminDeal | null>(null);
  const [loading, setLoading] = useState(true);
  const [dealId, setDealId] = useState<string | null>(null);

  // Unwrap params using React.use()
  useEffect(() => {
    const unwrapParams = async () => {
      const resolvedParams = await params;
      setDealId(resolvedParams.id);
    };
    unwrapParams();
  }, [params]);

  useEffect(() => {
    if (!dealId) return;

    const fetchDeal = async () => {
      try {
        setLoading(true);
        const fetchedDeal = await getDeal(dealId);
        setDeal(fetchedDeal);
      } catch (error) {
        console.error('Error fetching deal:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch deal details',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDeal();
  }, [dealId, toast]);

  const handleDelete = async () => {
    if (!deal || !confirm('Are you sure you want to delete this deal?')) return;

    try {
      const success = await deleteDeal(deal.id);
      if (success) {
        toast({
          title: 'Success',
          description: 'Deal deleted successfully'
        });
        router.push('/admin/deals');
      } else {
        throw new Error('Failed to delete deal');
      }
    } catch (error) {
      console.error('Error deleting deal:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete deal',
        variant: 'destructive'
      });
    }
  };

  const getDealTypeColor = (type: string) => {
    switch (type) {
      case 'flash': return 'bg-red-100 text-red-800';
      case 'clearance': return 'bg-orange-100 text-orange-800';
      case 'seasonal': return 'bg-blue-100 text-blue-800';
      case 'regular': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isExpired = (endDate: string) => {
    return new Date(endDate) < new Date();
  };

  const isActive = (startDate: string, endDate: string) => {
    const now = new Date();
    return new Date(startDate) <= now && new Date(endDate) >= now;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <AdminHeader
          title="Deal Details"
          description="Loading deal information..."
          breadcrumbs={[
            { label: 'Deals', href: '/admin/deals' },
            { label: 'Deal Details' }
          ]}
        />
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!deal) {
    return (
      <div className="space-y-6">
        <AdminHeader
          title="Deal Not Found"
          description="The requested deal could not be found"
          breadcrumbs={[
            { label: 'Deals', href: '/admin/deals' },
            { label: 'Deal Details' }
          ]}
        />
        <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
          <p className="text-gray-500 mb-4">Deal not found</p>
          <Link href="/admin/deals">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Deals
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AdminHeader
        title={deal.title}
        description="Deal details and management"
        breadcrumbs={[
          { label: 'Deals', href: '/admin/deals' },
          { label: deal.title }
        ]}
        actions={
          <div className="flex space-x-2">
            <Link href={`/admin/deals/${deal.id}/edit`}>
              <Button variant="outline">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </Link>
            <Button
              variant="outline"
              onClick={handleDelete}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        }
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Deal Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Deal Overview */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h3 className="text-lg font-medium mb-2">{deal.title}</h3>
                {deal.description && (
                  <p className="text-gray-600">{deal.description}</p>
                )}
              </div>
              <div className="flex space-x-2">
                <StatusBadge
                  status={deal.is_active ? 'active' : 'inactive'}
                  variant={deal.is_active ? 'success' : 'secondary'}
                />
                {deal.featured && (
                  <Badge className="bg-yellow-100 text-yellow-800">Featured</Badge>
                )}
                <Badge className={getDealTypeColor(deal.deal_type)}>
                  {deal.deal_type.charAt(0).toUpperCase() + deal.deal_type.slice(1)}
                </Badge>
              </div>
            </div>

            {/* Pricing Information */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-sm text-gray-500 mb-1">Original Price</div>
                <div className="text-lg font-medium line-through text-gray-500">
                  {formatCurrency(deal.original_price, deal.currency)}
                </div>
              </div>
              <div className="text-center">
                <div className="text-sm text-gray-500 mb-1">Deal Price</div>
                <div className="text-xl font-bold text-primary-600">
                  {formatCurrency(deal.deal_price, deal.currency)}
                </div>
              </div>
              <div className="text-center">
                <div className="text-sm text-gray-500 mb-1">Discount</div>
                <div className="text-lg font-bold text-green-600">
                  {deal.discount_percentage}% OFF
                </div>
                <div className="text-sm text-gray-500">
                  Save {formatCurrency(deal.original_price - deal.deal_price, deal.currency)}
                </div>
              </div>
            </div>
          </div>

          {/* Product Information */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-medium mb-4 flex items-center">
              <Package className="h-5 w-5 mr-2" />
              Product Information
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-500">Product Name:</span>
                <span className="font-medium">{deal.product_name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Store:</span>
                <span className="font-medium">{deal.store_name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Product Slug:</span>
                <span className="font-mono text-sm">{deal.product_slug}</span>
              </div>
            </div>
          </div>

          {/* Deal Timeline */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-medium mb-4 flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Deal Timeline
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium">Start Date</div>
                  <div className="text-sm text-gray-500">{formatDate(deal.start_date)}</div>
                </div>
                <div className="text-green-600">
                  <Clock className="h-5 w-5" />
                </div>
              </div>
              <div className={`flex items-center justify-between p-3 rounded-lg ${
                isExpired(deal.end_date) ? 'bg-red-50' : 'bg-gray-50'
              }`}>
                <div>
                  <div className="font-medium">End Date</div>
                  <div className={`text-sm ${
                    isExpired(deal.end_date) ? 'text-red-600' : 'text-gray-500'
                  }`}>
                    {formatDate(deal.end_date)}
                    {isExpired(deal.end_date) && ' (Expired)'}
                  </div>
                </div>
                <div className={isExpired(deal.end_date) ? 'text-red-600' : 'text-gray-400'}>
                  <Clock className="h-5 w-5" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Deal Status */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-medium mb-4">Deal Status</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-500">Status:</span>
                <StatusBadge
                  status={deal.is_active ? 'active' : 'inactive'}
                  variant={deal.is_active ? 'success' : 'secondary'}
                />
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-500">Currently:</span>
                <Badge className={
                  isActive(deal.start_date, deal.end_date)
                    ? 'bg-green-100 text-green-800'
                    : isExpired(deal.end_date)
                    ? 'bg-red-100 text-red-800'
                    : 'bg-yellow-100 text-yellow-800'
                }>
                  {isActive(deal.start_date, deal.end_date)
                    ? 'Running'
                    : isExpired(deal.end_date)
                    ? 'Expired'
                    : 'Scheduled'
                  }
                </Badge>
              </div>
              {deal.featured && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-500">Featured:</span>
                  <Badge className="bg-yellow-100 text-yellow-800">Yes</Badge>
                </div>
              )}
            </div>
          </div>

          {/* Quantity Limits */}
          {deal.max_quantity && (
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-medium mb-4">Quantity Limits</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-500">Max Quantity:</span>
                  <span className="font-medium">{deal.max_quantity}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Used:</span>
                  <span className="font-medium">{deal.used_quantity}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Remaining:</span>
                  <span className="font-medium text-green-600">
                    {deal.max_quantity - deal.used_quantity}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary-600 h-2 rounded-full"
                    style={{
                      width: `${(deal.used_quantity / deal.max_quantity) * 100}%`
                    }}
                  ></div>
                </div>
              </div>
            </div>
          )}

          {/* Timestamps */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-medium mb-4">Timestamps</h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Created:</span>
                <span>{formatDate(deal.created_at)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Updated:</span>
                <span>{formatDate(deal.updated_at)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
