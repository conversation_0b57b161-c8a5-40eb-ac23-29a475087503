'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Clock, Filter, Tag, Percent, ArrowLeft } from 'lucide-react';
import EcommerceLayout from '@/components/ecommerce/EcommerceLayout';
import { Button } from '@/components/ui/button';
import { ProductCard } from '@/features/products/components/ProductCard';
import { DealsService, DealProduct } from '@/lib/services/deals';

export default function DealsPage() {
  const [products, setProducts] = useState<DealProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState<'discount' | 'price' | 'name' | 'newest'>('discount');
  const [filterBy, setFilterBy] = useState<'all' | 'flash' | 'clearance' | 'seasonal'>('all');

  useEffect(() => {
    fetchDeals();
  }, [sortBy, filterBy]);

  const fetchDeals = async () => {
    try {
      setLoading(true);

      // Use the DealsService to get filtered and sorted deals
      const dealProducts = await DealsService.getDeals({
        dealType: filterBy === 'all' ? undefined : filterBy,
        sortBy,
        limit: 100
      });

      setProducts(dealProducts);
    } catch (error) {
      console.error('Error fetching deals:', error);
    } finally {
      setLoading(false);
    }
  };



  const dealCategories = [
    { id: 'all', name: 'All Deals', icon: Tag },
    { id: 'flash', name: 'Flash Sales', icon: Clock, description: '30%+ off' },
    { id: 'clearance', name: 'Clearance', icon: Percent, description: '50%+ off' },
    { id: 'seasonal', name: 'Seasonal', icon: Filter, description: '20-49% off' },
  ];

  const sortOptions = [
    { id: 'discount', name: 'Highest Discount' },
    { id: 'price', name: 'Lowest Price' },
    { id: 'name', name: 'Name A-Z' },
    { id: 'newest', name: 'Newest First' },
  ];

  return (
    <EcommerceLayout>
      <div className="bg-gradient-to-r from-primary-500 to-primary-600 text-white">
        <div className="container mx-auto px-4 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">🔥 Amazing Deals</h1>
            <p className="text-xl opacity-90 mb-6">
              Save big on your favorite products - Limited time offers!
            </p>
            <div className="flex items-center justify-center space-x-6 text-sm">
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-1" />
                <span>Limited Time</span>
              </div>
              <div className="flex items-center">
                <Percent className="w-4 h-4 mr-1" />
                <span>Up to 70% Off</span>
              </div>
              <div className="flex items-center">
                <Tag className="w-4 h-4 mr-1" />
                <span>Best Prices</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-6">
          <Link href="/" className="hover:text-primary-600">Home</Link>
          <span>/</span>
          <span className="text-gray-900 font-medium">Deals</span>
        </nav>

        {/* Deal Categories */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          {dealCategories.map((category) => {
            const Icon = category.icon;
            return (
              <button
                key={category.id}
                onClick={() => setFilterBy(category.id as any)}
                className={`p-4 rounded-lg border-2 transition-all text-center ${
                  filterBy === category.id
                    ? 'border-primary-500 bg-primary-50 text-primary-700'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
              >
                <Icon className="w-6 h-6 mx-auto mb-2" />
                <div className="font-medium">{category.name}</div>
                {category.description && (
                  <div className="text-xs text-gray-500 mt-1">{category.description}</div>
                )}
              </button>
            );
          })}
        </div>

        {/* Filters and Sort */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4">
          <div className="flex items-center space-x-4">
            <span className="text-gray-700 font-medium">
              {products.length} {products.length === 1 ? 'deal' : 'deals'} found
            </span>
          </div>

          <div className="flex items-center space-x-4">
            <label className="text-sm text-gray-700">Sort by:</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              {sortOptions.map((option) => (
                <option key={option.id} value={option.id}>
                  {option.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Products Grid */}
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {[...Array(12)].map((_, index) => (
              <div key={index} className="bg-gray-200 rounded-lg h-80 animate-pulse"></div>
            ))}
          </div>
        ) : products.length === 0 ? (
          <div className="text-center py-12">
            <Tag className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No deals found</h3>
            <p className="text-gray-600 mb-6">
              {filterBy === 'all'
                ? "We don't have any deals available right now. Check back soon!"
                : `No deals found in the ${dealCategories.find(c => c.id === filterBy)?.name} category.`
              }
            </p>
            <Link href="/products">
              <Button>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Browse All Products
              </Button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {products.map((product) => (
              <div key={product.id} className="relative">
                <ProductCard
                  product={product}
                  hideStoreInfo={false}
                />

                {/* Time left overlay - positioned to avoid price overlap */}
                <div className="absolute top-2 right-12 z-30">
                  <div className="bg-black/80 text-white text-[10px] px-1.5 py-0.5 rounded flex items-center">
                    <Clock className="w-2.5 h-2.5 mr-1" />
                    {product.timeLeft}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Call to Action */}
        {products.length > 0 && (
          <div className="mt-12 text-center bg-gray-50 rounded-lg p-8">
            <h3 className="text-xl font-bold text-gray-900 mb-2">Don&apos;t Miss Out!</h3>
            <p className="text-gray-600 mb-4">
              These deals are for a limited time only. Grab them before they&apos;re gone!
            </p>
            <Link href="/products">
              <Button variant="outline">
                Browse More Products
              </Button>
            </Link>
          </div>
        )}
      </div>
    </EcommerceLayout>
  );
}
