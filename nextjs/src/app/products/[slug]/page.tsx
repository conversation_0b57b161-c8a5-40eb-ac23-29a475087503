'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { useProduct } from '@/features/products/queries';
import { ProductDetails } from '@/features/products/components';
import EcommerceLayout from '@/components/ecommerce/EcommerceLayout';

export default function ProductPage() {
  const { slug } = useParams();
  const productSlug = Array.isArray(slug) ? slug[0] : slug;
  
  const { data, isLoading, error } = useProduct(productSlug);
  
  return (
    <EcommerceLayout>
      {isLoading ? (
        <div className="container mx-auto px-4 py-12">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="aspect-square bg-gray-200 rounded-lg"></div>
              <div>
                <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-6"></div>
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-6"></div>
                <div className="h-10 bg-gray-200 rounded w-full mb-6"></div>
              </div>
            </div>
          </div>
        </div>
      ) : error ? (
        <div className="container mx-auto px-4 py-12 text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Product</h1>
          <p className="text-gray-600">
            We couldn&apos;t load the product information. Please try again later.
          </p>
        </div>
      ) : data?.product ? (
        <ProductDetails product={data.product} />
      ) : (
        <div className="container mx-auto px-4 py-12 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <p className="text-gray-600">
            The product you&apos;re looking for doesn&apos;t exist or has been removed.
          </p>
        </div>
      )}
    </EcommerceLayout>
  );
}
