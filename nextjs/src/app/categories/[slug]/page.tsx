'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { useCategory } from '@/features/categories/queries';
import { ProductGrid } from '@/features/products/components';
import EcommerceLayout from '@/components/ecommerce/EcommerceLayout';

export default function CategoryPage() {
  const { slug } = useParams();
  const categorySlug = Array.isArray(slug) ? slug[0] : slug;

  const { data, isLoading, error } = useCategory(categorySlug);

  return (
    <EcommerceLayout>
      {isLoading ? (
        <div className="container mx-auto px-4 py-12">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="h-64 bg-gray-200 rounded-lg mb-8"></div>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      ) : error ? (
        <div className="container mx-auto px-4 py-12 text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Category</h1>
          <p className="text-gray-600">
            We couldn't load the category information. Please try again later.
          </p>
        </div>
      ) : data?.category ? (
        <div>
          {/* Category Header */}
          <div className="relative">
            {/* Category Image */}
            <div className="h-64 relative">
              <Image
                src={(() => {
                  const imageUrl = data.category.image ||
                    (data.category as any).image_url ||
                    (data.category as any).imageUrl ||
                    (data.category as any).thumbnail ||
                    '/placeholder-category.jpg';

                  // Format Unsplash URLs for better performance
                  if (imageUrl.includes('unsplash.com')) {
                    // Remove any existing query parameters
                    const baseUrl = imageUrl.split('?')[0];
                    // Add our own optimization parameters
                    return `${baseUrl}?auto=format&fit=crop&w=1920&q=80`;
                  }

                  return imageUrl;
                })()}
                alt={data.category.name}
                fill
                className="object-cover"
                sizes="100vw"
                priority
              />
              <div className="absolute inset-0 bg-black bg-opacity-40"></div>
            </div>

            {/* Category Info */}
            <div className="absolute inset-0 flex items-center justify-center text-center">
              <div className="px-4">
                <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">
                  {data.category.name}
                </h1>
                {data.category.description && (
                  <p className="text-white text-lg max-w-2xl mx-auto">
                    {data.category.description}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Products */}
          <div className="container mx-auto px-4 py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Products in {data.category.name}</h2>

            {data.products && data.products.length > 0 ? (
              <ProductGrid products={data.products} />
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500">No products found in this category</p>
                <Link href="/" className="mt-4 inline-block text-primary-600 hover:text-primary-700">
                  Back to homepage
                </Link>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="container mx-auto px-4 py-12 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Category Not Found</h1>
          <p className="text-gray-600">
            The category you're looking for doesn't exist or has been removed.
          </p>
          <Link href="/" className="mt-4 inline-block text-primary-600 hover:text-primary-700">
            Back to homepage
          </Link>
        </div>
      )}
    </EcommerceLayout>
  );
}
