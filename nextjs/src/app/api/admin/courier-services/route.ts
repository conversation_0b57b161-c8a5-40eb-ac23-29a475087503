import { NextRequest, NextResponse } from 'next/server';
import { createServerAdminClient } from '@/lib/supabase/serverAdminClient';

// GET /api/admin/courier-services - List courier services
export async function GET(req: NextRequest) {
  try {
    console.log('[API] GET /api/admin/courier-services');
    
    const supabase = await createServerAdminClient();

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const perPage = parseInt(searchParams.get('per_page') || '10');
    const search = searchParams.get('search') || '';
    const isActive = searchParams.get('is_active');

    console.log('[API] Query params:', { page, perPage, search, isActive });

    // Calculate pagination
    const from = (page - 1) * perPage;
    const to = from + perPage - 1;

    // Build query
    let query = supabase
      .from('courier_services')
      .select('*', { count: 'exact' })
      .range(from, to)
      .order('created_at', { ascending: false });

    // Add search filter
    if (search) {
      query = query.ilike('name', `%${search}%`);
    }

    // Add is_active filter
    if (isActive !== null) {
      query = query.eq('is_active', isActive === 'true');
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('[API] Database error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log('[API] Query result:', { dataCount: data?.length, totalCount: count });

    const totalPages = Math.ceil((count || 0) / perPage);

    return NextResponse.json({
      data: data || [],
      meta: {
        total: count || 0,
        page,
        per_page: perPage,
        total_pages: totalPages,
      }
    });
  } catch (error) {
    console.error('[API] Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/courier-services - Create courier service
export async function POST(req: NextRequest) {
  try {
    console.log('[API] POST /api/admin/courier-services');
    
    const supabase = await createServerAdminClient();

    const body = await req.json();
    console.log('[API] Request body:', body);

    // Validate required fields
    if (!body.name || !body.estimated_delivery_time || !body.countries || body.base_cost === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: name, estimated_delivery_time, countries, base_cost' },
        { status: 400 }
      );
    }

    // Create courier service
    const { data, error } = await supabase
      .from('courier_services')
      .insert({
        name: body.name,
        description: body.description || null,
        estimated_delivery_time: body.estimated_delivery_time,
        base_cost: body.base_cost,
        cost_per_kg: body.cost_per_kg || null,
        free_weight_limit: body.free_weight_limit || null,
        tracking_supported: body.tracking_supported || false,
        countries: body.countries || [],
        is_active: body.is_active !== undefined ? body.is_active : true,
      })
      .select()
      .single();

    if (error) {
      console.error('[API] Database error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log('[API] Created courier service:', data);

    return NextResponse.json({ courierService: data }, { status: 201 });
  } catch (error) {
    console.error('[API] Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}