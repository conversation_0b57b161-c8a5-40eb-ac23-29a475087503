import { NextRequest, NextResponse } from 'next/server';
import { createServerAdminClient } from '@/lib/supabase/serverAdminClient';

export async function GET(request: NextRequest) {
  try {
    // Get the query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const featured = searchParams.get('featured');

    const supabase = await createServerAdminClient();

    // Build query
    let query = supabase.from('stores').select('*');

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    if (featured) {
      query = query.eq('featured', featured === 'true');
    }

    // Get results
    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // No need to transform data for owner email
    const stores = data;

    return NextResponse.json({
      stores: stores || [],
      count: stores?.length || 0
    });
  } catch (error) {
    console.error('Error in admin stores API:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate the request body
    if (!body || typeof body !== 'object' || !body.name || !body.slug || !body.owner_id) {
      return NextResponse.json({
        error: 'Invalid request body. Required fields: name, slug, owner_id'
      }, { status: 400 });
    }

    const supabase = await createServerAdminClient();

    // Add timestamps
    const storeData = {
      ...body,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Create the store
    const { data, error } = await supabase
      .from('stores')
      .insert(storeData)
      .select()
      .single();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating store:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
