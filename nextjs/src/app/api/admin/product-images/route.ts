import { NextRequest, NextResponse } from 'next/server';
import { createServerAdminClient } from '@/lib/supabase/serverAdminClient';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const productId = formData.get('product_id') as string;
    const file = formData.get('file') as File;
    const alt = formData.get('alt') as string || '';
    const position = parseInt(formData.get('position') as string || '0');

    if (!productId || !file) {
      return NextResponse.json({
        error: 'Missing required fields: product_id, file'
      }, { status: 400 });
    }

    const supabase = await createServerAdminClient();

    // Check if the product-images bucket exists, create it if it doesn't
    const { data: buckets } = await supabase.storage.listBuckets();
    const bucketExists = buckets?.some(bucket => bucket.name === 'product-images');

    if (!bucketExists) {
      console.log('Creating product-images bucket');
      const { error: createBucketError } = await supabase.storage.createBucket('product-images', {
        public: true
      });

      if (createBucketError) {
        console.error('Error creating bucket:', createBucketError);
        return NextResponse.json({ error: createBucketError.message }, { status: 500 });
      }
    }

    // Upload the file to Supabase Storage
    const fileName = `${Date.now()}-${file.name.replace(/\s+/g, '-')}`;
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('product-images')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      console.error('Error uploading file:', uploadError);
      return NextResponse.json({ error: uploadError.message }, { status: 500 });
    }

    // Get the public URL for the uploaded file
    const { data: { publicUrl } } = supabase.storage
      .from('product-images')
      .getPublicUrl(fileName);

    // Create a record in the product_images table
    try {
      const { data, error } = await supabase
        .from('product_images')
        .insert({
          product_id: productId,
          url: publicUrl,
          alt,
          position
        })
        .select()
        .single();

      if (error) {
        console.error('Error inserting product image record:', error);
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data);
    } catch (dbError) {
      console.error('Exception inserting product image record:', dbError);

      // If there's an error with the database, still return the URL so the frontend can display it
      return NextResponse.json({
        url: publicUrl,
        error: dbError instanceof Error ? dbError.message : 'Database error occurred, but image was uploaded'
      });
    }
  } catch (error) {
    console.error('Error uploading product image:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
