import { NextRequest, NextResponse } from 'next/server';
import { createServerAdminClient } from '@/lib/supabase/serverAdminClient';

export async function GET(request: NextRequest) {
  try {
    console.log('[API GET /admin/delivery-services] Received request:', request.url);
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const perPage = parseInt(searchParams.get('per_page') || '10');
    const search = searchParams.get('search') || '';
    const isActive = searchParams.get('is_active');

    const supabase = await createServerAdminClient();

    // Build query
    let query = supabase
      .from('external_delivery_services')
      .select('*', { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    if (isActive !== null && isActive !== '') {
      query = query.eq('is_active', isActive === 'true');
    }

    // Apply pagination
    const from = (page - 1) * perPage;
    const to = from + perPage - 1;
    query = query.range(from, to);

    // Order by name
    query = query.order('name', { ascending: true });

    console.log('[API GET /admin/delivery-services] Executing Supabase query...');
    const { data: services, error, count } = await query;
    console.log('[API GET /admin/delivery-services] Supabase query result:', { services, error, count });

    if (error) {
      console.error('Error fetching delivery services:', error);
      return NextResponse.json(
        { error: 'Failed to fetch delivery services' },
        { status: 500 }
      );
    }

    const responsePayload = {
      services: services || [],
      total: count || 0,
      page,
      per_page: perPage,
      total_pages: Math.ceil((count || 0) / perPage),
    };
    console.log('[API GET /admin/delivery-services] Sending response:', responsePayload);
    return NextResponse.json(responsePayload);
  } catch (error) {
    console.error('Error in delivery services API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      description,
      logo,
      contact_phone,
      contact_email,
      website,
      base_fee,
      per_km_fee,
      minimum_order_amount,
      maximum_delivery_radius_km,
      estimated_delivery_time,
      is_active,
      coverage_areas,
      operating_hours,
    } = body;

    // Validate required fields
    if (!name || base_fee === undefined) {
      return NextResponse.json(
        { error: 'Name and base fee are required' },
        { status: 400 }
      );
    }

    const supabase = await createServerAdminClient();

    const { data: service, error } = await supabase
      .from('external_delivery_services')
      .insert({
        name,
        description,
        logo,
        contact_phone,
        contact_email,
        website,
        base_fee: parseFloat(base_fee),
        per_km_fee: per_km_fee ? parseFloat(per_km_fee) : null,
        minimum_order_amount: minimum_order_amount ? parseFloat(minimum_order_amount) : null,
        maximum_delivery_radius_km: maximum_delivery_radius_km ? parseInt(maximum_delivery_radius_km) : null,
        estimated_delivery_time,
        is_active: is_active !== undefined ? is_active : true,
        coverage_areas: coverage_areas || [],
        operating_hours: operating_hours || {},
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating delivery service:', error);
      return NextResponse.json(
        { error: 'Failed to create delivery service' },
        { status: 500 }
      );
    }

    return NextResponse.json({ service }, { status: 201 });
  } catch (error) {
    console.error('Error in delivery service creation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
