import { NextRequest, NextResponse } from 'next/server';
import { createServerAdminClient } from '@/lib/supabase/serverAdminClient';

interface RouteParams {
  params: { id: string };
}

// GET a single delivery service by ID
export async function GET(request: NextRequest, { params }: RouteParams) {
  const { id } = params;
  console.log(`[API GET /admin/delivery-services/${id}] Received request`);
  try {
    const supabase = await createServerAdminClient();
    const { data, error } = await supabase
      .from('external_delivery_services')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // PostgREST error code for "Not found"
        console.error(`[API GET /admin/delivery-services/${id}] Service not found:`, error);
        return NextResponse.json({ error: 'Delivery service not found' }, { status: 404 });
      }
      console.error(`[API GET /admin/delivery-services/${id}] Error fetching service:`, error);
      return NextResponse.json({ error: 'Failed to fetch delivery service' }, { status: 500 });
    }

    if (!data) {
      return NextResponse.json({ error: 'Delivery service not found' }, { status: 404 });
    }
    console.log(`[API GET /admin/delivery-services/${id}] Sending service data:`, data);
    return NextResponse.json({ service: data });
  } catch (error: any) {
    console.error(`[API GET /admin/delivery-services/${id}] Internal server error:`, error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}

// UPDATE a delivery service by ID
export async function PUT(request: NextRequest, { params }: RouteParams) {
  const { id } = params;
  console.log(`[API PUT /admin/delivery-services/${id}] Received request`);
  try {
    const body = await request.json();
    // Basic validation (can be expanded)
    if (!body || Object.keys(body).length === 0) {
        return NextResponse.json({ error: 'Request body cannot be empty' }, { status: 400 });
    }
    
    const supabase = await createServerAdminClient();
    const { data, error } = await supabase
      .from('external_delivery_services')
      .update(body)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        console.error(`[API PUT /admin/delivery-services/${id}] Service not found for update:`, error);
        return NextResponse.json({ error: 'Delivery service not found' }, { status: 404 });
      }
      console.error(`[API PUT /admin/delivery-services/${id}] Error updating service:`, error);
      return NextResponse.json({ error: 'Failed to update delivery service' }, { status: 500 });
    }
    console.log(`[API PUT /admin/delivery-services/${id}] Service updated:`, data);
    return NextResponse.json({ service: data });
  } catch (error: any) {
    console.error(`[API PUT /admin/delivery-services/${id}] Internal server error:`, error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}

// DELETE a delivery service by ID
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  const { id } = params;
  console.log(`[API DELETE /admin/delivery-services/${id}] Received request`);
  try {
    const supabase = await createServerAdminClient();
    const { error } = await supabase
      .from('external_delivery_services')
      .delete()
      .eq('id', id);

    if (error) {
      if (error.code === 'PGRST116') { // Should not happen for delete if ID exists, but good practice
        console.error(`[API DELETE /admin/delivery-services/${id}] Service not found for deletion:`, error);
        return NextResponse.json({ error: 'Delivery service not found' }, { status: 404 });
      }
      console.error(`[API DELETE /admin/delivery-services/${id}] Error deleting service:`, error);
      return NextResponse.json({ error: 'Failed to delete delivery service' }, { status: 500 });
    }
    console.log(`[API DELETE /admin/delivery-services/${id}] Service deleted successfully`);
    return NextResponse.json({ message: 'Delivery service deleted successfully' }, { status: 200 });
  } catch (error: any) {
    console.error(`[API DELETE /admin/delivery-services/${id}] Internal server error:`, error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}