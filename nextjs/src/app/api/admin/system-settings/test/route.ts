import { NextRequest, NextResponse } from 'next/server';
import { SystemSettingsService } from '@/lib/services/system-settings';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing SystemSettingsService...');
    
    // Test getting the service fee config
    const config = await SystemSettingsService.getServiceFeeConfig();
    console.log('Service fee config:', config);
    
    // Test calculating service fee
    const fee = await SystemSettingsService.calculateServiceFee(500);
    console.log('Service fee for 500:', fee);
    
    return NextResponse.json({
      success: true,
      config,
      testFee: fee
    });

  } catch (error) {
    console.error('Error testing system settings:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}