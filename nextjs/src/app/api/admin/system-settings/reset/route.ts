import { NextRequest, NextResponse } from 'next/server';
import { createSPASassClient } from '@/lib/supabase/client';
import { SystemSettingsService } from '@/lib/services/system-settings';

export async function POST(request: NextRequest) {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Delete existing service fee configuration
    const { error: deleteError } = await supabase
      .from('system_settings')
      .delete()
      .eq('setting_key', 'service_fee_config');

    if (deleteError) {
      console.error('Error deleting existing config:', deleteError);
      return NextResponse.json({
        success: false,
        message: 'Failed to clear existing configuration',
        error: deleteError.message
      }, { status: 500 });
    }

    // Initialize fresh configuration
    const initialized = await SystemSettingsService.initializeServiceFeeConfig();
    
    if (initialized) {
      const config = await SystemSettingsService.getServiceFeeConfig();
      
      return NextResponse.json({
        success: true,
        message: 'System settings reset and reinitialized successfully',
        config
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'Failed to reinitialize system settings after reset'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error resetting system settings:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}