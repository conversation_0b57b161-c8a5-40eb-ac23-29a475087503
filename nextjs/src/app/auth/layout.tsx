import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';

export default function AuthLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const productName = process.env.NEXT_PUBLIC_PRODUCTNAME || 'Finder';

    return (
        <div className="relative min-h-screen bg-gray-50">
            {/* Back to homepage button */}
            <div className="absolute top-6 left-6 z-10">
                <Link
                    href="/"
                    className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors shadow-sm"
                >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to {productName}
                </Link>
            </div>

            {/* Main content */}
            {children}
        </div>
    );
}