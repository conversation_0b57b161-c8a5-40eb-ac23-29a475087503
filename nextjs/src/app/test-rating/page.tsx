'use client';

import React from 'react';
import EcommerceLayout from '@/components/ecommerce/EcommerceLayout';
import OrderRatingButton from '@/components/orders/OrderRatingButton';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestRatingPage() {
  // Mock data for testing
  const mockDeliveredOrder = {
    id: 'test-order-123',
    status: 'delivered',
    stores: [
      { id: 'store-1', name: 'TechHub Gambia' },
      { id: 'store-2', name: 'Fashion Forward GM' }
    ]
  };

  const mockPendingOrder = {
    id: 'test-order-456',
    status: 'pending',
    stores: [
      { id: 'store-3', name: 'Home Essentials GM' }
    ]
  };

  return (
    <EcommerceLayout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Test Rating System</h1>
            <p className="text-gray-600">
              This page shows how the rating system works for different order statuses.
            </p>
          </div>

          <div className="space-y-6">
            {/* Delivered Order - Should show rating buttons */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Delivered Order (Shows Rating)</CardTitle>
                <p className="text-sm text-gray-600">
                  Order #{mockDeliveredOrder.id} - Status: {mockDeliveredOrder.status}
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-sm text-gray-600">
                    <strong>Sold by:</strong>
                    <p className="mt-1">Finder Marketplace</p>
                  </div>
                  
                  <div className="border-t pt-4">
                    <OrderRatingButton 
                      orderId={mockDeliveredOrder.id}
orderStatus                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Pending Order - Should NOT show rating buttons */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Pending Order (No Rating)</CardTitle>
                <p className="text-sm text-gray-600">
                  Order #{mockPendingOrder.id} - Status: {mockPendingOrder.status}
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-sm text-gray-600">
                    <strong>Sold by:</strong>
                    <p className="mt-1">Finder Marketplace</p>
                  </div>
                  
                  <div className="border-t pt-4">
                    <OrderRatingButton 
                      orderId={mockPendingOrder.id}
orderStatus                    />
                    <p className="text-sm text-gray-500 mt-2">
                      ↑ No rating buttons should appear above because this order is not delivered yet.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Instructions */}
            <Card className="bg-blue-50 border-blue-200">
              <CardHeader>
                <CardTitle className="text-lg text-blue-900">How Rating Works</CardTitle>
              </CardHeader>
              <CardContent className="text-blue-800">
                <div className="space-y-2 text-sm">
                  <p><strong>1. Order Status:</strong> Rating buttons only appear for orders with status "delivered" or "completed"</p>
                  <p><strong>2. Store Rating:</strong> You can rate each store separately from a delivered order</p>
                  <p><strong>3. One Rating Per Store:</strong> Once you rate a store, the button disappears for that store</p>
                  <p><strong>4. Location:</strong> In real usage, these buttons appear in:</p>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    <li><strong>My Orders page:</strong> /my-orders (in each order card)</li>
                    <li><strong>Order Details page:</strong> /my-orders/[order-id]</li>
                  </ul>
                  <p><strong>5. Store Reviews:</strong> After rating, reviews appear on store pages: /stores/[store-slug]</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </EcommerceLayout>
  );
}