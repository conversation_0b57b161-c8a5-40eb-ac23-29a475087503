import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getUserProfile, updateUserProfile } from './api';
import { UpdateProfileParams, UserProfile } from './types';

// Query keys
export const userKeys = {
  all: ['user'] as const,
  profile: () => [...userKeys.all, 'profile'] as const,
};

// Hooks
export function useUserProfile() {
  return useQuery({
    queryKey: userKeys.profile(),
    queryFn: async () => {
      const { profile, error } = await getUserProfile();
      if (error) {
        throw new Error(error.message);
      }
      return profile;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useUpdateProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: UpdateProfileParams) => {
      const { profile, error } = await updateUserProfile(params);
      if (error) {
        throw new Error(error.message);
      }
      return profile;
    },
    onSuccess: (updatedProfile: UserProfile | null) => {
      // Update the profile cache
      queryClient.setQueryData(userKeys.profile(), updatedProfile);
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: userKeys.all });
    },
  });
}
