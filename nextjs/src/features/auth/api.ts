import { createSPASassClient } from '@/lib/supabase/client';
import { 
  LoginCredentials, 
  RegisterCredentials, 
  ResetPasswordCredentials,
  AuthResponse,
  MFASetupResponse,
  MFAVerifyResponse
} from './types';

export async function login(credentials: LoginCredentials): Promise<AuthResponse> {
  try {
    const client = await createSPASassClient();
    const { data, error } = await client.login(credentials.email, credentials.password);
    
    if (error) {
      return { user: null, error: new Error(error.message) };
    }
    
    return { user: data.user, error: null };
  } catch (error) {
    return { 
      user: null, 
      error: error instanceof Error ? error : new Error('An unknown error occurred during login') 
    };
  }
}

export async function register(credentials: RegisterCredentials): Promise<AuthResponse> {
  try {
    if (credentials.password !== credentials.passwordConfirm) {
      return { user: null, error: new Error('Passwords do not match') };
    }
    
    const client = await createSPASassClient();
    const { data, error } = await client.register(credentials.email, credentials.password);
    
    if (error) {
      return { user: null, error: new Error(error.message) };
    }
    
    return { user: data.user, error: null };
  } catch (error) {
    return { 
      user: null, 
      error: error instanceof Error ? error : new Error('An unknown error occurred during registration') 
    };
  }
}

export async function logout(): Promise<{ error: Error | null }> {
  try {
    const client = await createSPASassClient();
    await client.logout();
    return { error: null };
  } catch (error) {
    return { 
      error: error instanceof Error ? error : new Error('An unknown error occurred during logout') 
    };
  }
}

export async function resetPassword(
  token: string, 
  credentials: ResetPasswordCredentials
): Promise<{ success: boolean; error: Error | null }> {
  try {
    if (credentials.password !== credentials.passwordConfirm) {
      return { success: false, error: new Error('Passwords do not match') };
    }
    
    const client = await createSPASassClient();
    const { error } = await client.resetPassword(token, credentials.password);
    
    if (error) {
      return { success: false, error: new Error(error.message) };
    }
    
    return { success: true, error: null };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error : new Error('An unknown error occurred during password reset') 
    };
  }
}

export async function requestPasswordReset(email: string): Promise<{ success: boolean; error: Error | null }> {
  try {
    const client = await createSPASassClient();
    const { error } = await client.requestPasswordReset(email);
    
    if (error) {
      return { success: false, error: new Error(error.message) };
    }
    
    return { success: true, error: null };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error : new Error('An unknown error occurred') 
    };
  }
}

export async function setupMFA(): Promise<MFASetupResponse> {
  try {
    const client = await createSPASassClient();
    const { data, error } = await client.setupMFA();
    
    if (error) {
      return { qr: '', secret: '', error: new Error(error.message) };
    }
    
    return { qr: data.qr, secret: data.secret };
  } catch (error) {
    return { 
      qr: '', 
      secret: '', 
      error: error instanceof Error ? error : new Error('An unknown error occurred during MFA setup') 
    };
  }
}

export async function verifyMFA(code: string): Promise<MFAVerifyResponse> {
  try {
    const client = await createSPASassClient();
    const { error } = await client.verifyMFA(code);
    
    if (error) {
      return { success: false, error: new Error(error.message) };
    }
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error : new Error('An unknown error occurred during MFA verification') 
    };
  }
}
