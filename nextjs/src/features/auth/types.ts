import { User } from '@supabase/supabase-js';

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: Error | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  passwordConfirm: string;
}

export interface ResetPasswordCredentials {
  password: string;
  passwordConfirm: string;
}

export interface AuthResponse {
  user: User | null;
  error: Error | null;
}

export interface MFASetupResponse {
  qr: string;
  secret: string;
  error?: Error;
}

export interface MFAVerifyResponse {
  success: boolean;
  error?: Error;
}
