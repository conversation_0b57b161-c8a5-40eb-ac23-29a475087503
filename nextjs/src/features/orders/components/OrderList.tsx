'use client';

import React, { useState } from 'react';
import { useUserOrders } from '../queries';
import { OrderCard } from './OrderCard';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Package,
  Filter,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { OrderListParams } from '../api';

export function OrderList() {
  const [params, setParams] = useState<OrderListParams>({
    page: 1,
    per_page: 6,
    sort_by: 'created_at',
    sort_order: 'desc'
  });

  const { data, isLoading, error } = useUserOrders(params);

  const handleStatusFilter = (status: string) => {
    setParams(prev => ({
      ...prev,
      status: status === 'all' ? undefined : status,
      page: 1
    }));
  };

  const handlePageChange = (newPage: number) => {
    setParams(prev => ({
      ...prev,
      page: newPage
    }));
  };

  const totalPages = data ? Math.ceil(data.count / (params.per_page || 6)) : 0;

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-gray-100 rounded-lg h-48 animate-pulse" />
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Error loading orders
        </h3>
        <p className="text-gray-500">
          There was an error loading your orders. Please try again.
        </p>
      </div>
    );
  }

  if (!data?.orders.length) {
    return (
      <div className="text-center py-12">
        <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No orders found
        </h3>
        <p className="text-gray-500 mb-6">
          {params.status 
            ? `You don't have any ${params.status} orders yet.`
            : "You haven't placed any orders yet."
          }
        </p>
        <Button asChild>
          <a href="/">Start Shopping</a>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium">Filter by status:</span>
          </div>
          <Select 
            value={params.status || 'all'} 
            onValueChange={handleStatusFilter}
          >
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Orders</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="shipped">Shipped</SelectItem>
              <SelectItem value="delivered">Delivered</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="text-sm text-gray-500">
          Showing {data.orders.length} of {data.count} orders
        </div>
      </div>

      {/* Orders Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {data.orders.map((order) => (
          <OrderCard key={order.id} order={order} />
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center gap-2 mt-8">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(params.page! - 1)}
            disabled={params.page === 1}
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
          
          <div className="flex items-center gap-1">
            {[...Array(totalPages)].map((_, i) => {
              const page = i + 1;
              const isCurrentPage = page === params.page;
              
              // Show first page, last page, current page, and pages around current
              const showPage = 
                page === 1 || 
                page === totalPages || 
                Math.abs(page - (params.page || 1)) <= 1;
              
              if (!showPage) {
                // Show ellipsis for gaps
                if (page === 2 && (params.page || 1) > 4) {
                  return <span key={page} className="px-2 text-gray-400">...</span>;
                }
                if (page === totalPages - 1 && (params.page || 1) < totalPages - 3) {
                  return <span key={page} className="px-2 text-gray-400">...</span>;
                }
                return null;
              }
              
              return (
                <Button
                  key={page}
                  variant={isCurrentPage ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageChange(page)}
                  className="w-8 h-8 p-0"
                >
                  {page}
                </Button>
              );
            })}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(params.page! + 1)}
            disabled={params.page === totalPages}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
}
