import { createSPASassClient } from '@/lib/supabase/client';

export interface UserOrder {
  id: string;
  status: string;
  total: number;
  currency: string;
  created_at: string;
  updated_at: string;
  shipping_name?: string;
  shipping_email?: string;
  shipping_phone?: string;
  shipping_address?: string;
  shipping_city?: string;
  shipping_state?: string;
  shipping_country?: string;
  shipping_postal_code?: string;
  notes?: string;
  delivery_method?: string;
  selected_delivery_service_id?: string;
  delivery_service?: {
    name: string;
    estimated_delivery_time?: string;
  };
  items_count: number;
  items?: OrderItem[];
  stores?: Array<{
    id: string;
    name: string;
  }>;
}

export interface OrderItem {
  id: string;
  product_id: string;
  store_id: string;
  quantity: number;
  price: number;
  total: number;
  product?: {
    name: string;
    slug: string;
    images?: { url: string }[];
  };
  store?: {
    name: string;
    slug: string;
  };
}

export interface OrderListParams {
  page?: number;
  per_page?: number;
  status?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

// Get orders for the current user
export async function getUserOrders(params: OrderListParams = {}): Promise<{ orders: UserOrder[], count: number }> {
  try {
    const {
      page = 1,
      per_page = 10,
      status,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = params;

    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Build query for orders with delivery service and store information
    let query = supabase.from('orders')
      .select(`
        *, 
        order_items(id, store_id, stores(id, name)),
        external_delivery_services(
          name,
          estimated_delivery_time
        )
      `, { count: 'exact' })
      .eq('user_id', user.id);

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    // Apply pagination
    const from = (page - 1) * per_page;
    const to = from + per_page - 1;

    // Get results
    const { data, error, count } = await query
      .order(sort_by, { ascending: sort_order === 'asc' })
      .range(from, to);

    if (error) throw error;

    // Transform data to include items count, delivery information, and stores
    const orders = data.map((order: any) => {
      // Extract unique stores from order items
      const stores = order.order_items?.reduce((uniqueStores: any[], item: any) => {
        if (item.stores && !uniqueStores.find(s => s.id === item.stores.id)) {
          uniqueStores.push({
            id: item.stores.id,
            name: item.stores.name
          });
        }
        return uniqueStores;
      }, []) || [];

      return {
        ...order,
        items_count: order.order_items?.length || 0,
        stores,
        delivery_service: order.external_delivery_services ? {
          name: order.external_delivery_services.name,
          estimated_delivery_time: order.external_delivery_services.estimated_delivery_time
        } : undefined,
        order_items: undefined,
        external_delivery_services: undefined
      };
    });

    return {
      orders: orders as UserOrder[],
      count: count || 0
    };
  } catch (error) {
    console.error('Error getting user orders:', error);
    return { orders: [], count: 0 };
  }
}

// Get a specific order for the current user
export async function getUserOrder(id: string): Promise<UserOrder | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Get the order data with delivery service information
    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .select(`
        *,
        external_delivery_services(
          name,
          estimated_delivery_time
        )
      `)
      .eq('id', id)
      .eq('user_id', user.id) // Ensure user can only access their own orders
      .single();

    if (orderError) {
      console.error('Error fetching order:', orderError);
      return null;
    }

    if (!orderData) {
      return null;
    }

    // Get order items with product and store details
    const { data: itemsData } = await supabase
      .from('order_items')
      .select(`
        *,
        products(name, slug),
        stores(name, slug)
      `)
      .eq('order_id', id);

    // Get product images for each item
    const itemsWithImages = await Promise.all(
      (itemsData || []).map(async (item) => {
        const { data: images } = await supabase
          .from('product_images')
          .select('url')
          .eq('product_id', item.product_id)
          .order('position')
          .limit(1);

        return {
          id: item.id,
          product_id: item.product_id,
          store_id: item.store_id,
          quantity: item.quantity,
          price: item.price,
          total: item.total,
          product: {
            name: item.products?.name || 'Unknown Product',
            slug: item.products?.slug || '',
            images: images || []
          },
          store: {
            name: item.stores?.name || 'Unknown Store',
            slug: item.stores?.slug || ''
          }
        };
      })
    );

    // Extract unique stores from order items
    const stores = itemsWithImages.reduce((uniqueStores: any[], item: any) => {
      if (item.store && !uniqueStores.find(s => s.id === item.store_id)) {
        uniqueStores.push({
          id: item.store_id,
          name: item.store.name
        });
      }
      return uniqueStores;
    }, []);

    // Return the order with items and delivery information
    return {
      id: orderData.id,
      status: orderData.status,
      total: orderData.total,
      currency: orderData.currency || 'GMD',
      created_at: orderData.created_at,
      updated_at: orderData.updated_at,
      shipping_name: orderData.shipping_name,
      shipping_email: orderData.shipping_email,
      shipping_phone: orderData.shipping_phone,
      shipping_address: orderData.shipping_address,
      shipping_city: orderData.shipping_city,
      shipping_state: orderData.shipping_state,
      shipping_country: orderData.shipping_country,
      shipping_postal_code: orderData.shipping_postal_code,
      notes: orderData.notes,
      delivery_method: orderData.delivery_method,
      selected_delivery_service_id: orderData.selected_delivery_service_id,
      delivery_service: orderData.external_delivery_services ? {
        name: orderData.external_delivery_services.name,
        estimated_delivery_time: orderData.external_delivery_services.estimated_delivery_time
      } : undefined,
      items_count: itemsWithImages.length,
      items: itemsWithImages,
      stores: stores
    };
  } catch (error) {
    console.error('Error getting user order:', error);
    return null;
  }
}

// Get order statistics for the current user
export async function getUserOrderStats(): Promise<{
  total_orders: number;
  pending_orders: number;
  completed_orders: number;
  total_spent: number;
}> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Get all orders for the user
    const { data: orders, error } = await supabase
      .from('orders')
      .select('status, total')
      .eq('user_id', user.id);

    if (error) throw error;

    const stats = {
      total_orders: orders?.length || 0,
      pending_orders: orders?.filter(o => ['pending', 'processing'].includes(o.status)).length || 0,
      completed_orders: orders?.filter(o => o.status === 'delivered').length || 0,
      // Only count orders that have been successfully delivered or picked up
      total_spent: orders?.filter(o => ['delivered', 'picked_up'].includes(o.status))
        .reduce((sum, order) => sum + (order.total || 0), 0) || 0
    };

    return stats;
  } catch (error) {
    console.error('Error getting user order stats:', error);
    return {
      total_orders: 0,
      pending_orders: 0,
      completed_orders: 0,
      total_spent: 0
    };
  }
}
