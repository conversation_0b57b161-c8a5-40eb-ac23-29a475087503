'use client';

import { useQuery } from '@tanstack/react-query';
import { getUserOrders, getUserOrder, getUserOrderStats } from './api';
import type { OrderListParams } from './api';

// Query keys
export const orderKeys = {
  all: ['user-orders'] as const,
  lists: () => [...orderKeys.all, 'list'] as const,
  list: (params: OrderListParams) => [...orderKeys.lists(), params] as const,
  details: () => [...orderKeys.all, 'detail'] as const,
  detail: (id: string) => [...orderKeys.details(), id] as const,
  stats: () => [...orderKeys.all, 'stats'] as const,
};

// Get user orders with pagination and filtering
export function useUserOrders(params: OrderListParams = {}) {
  return useQuery({
    queryKey: orderKeys.list(params),
    queryFn: () => getUserOrders(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get a specific order by ID
export function useUserOrder(id: string) {
  return useQuery({
    queryKey: orderKeys.detail(id),
    queryFn: () => getUserOrder(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get user order statistics
export function useUserOrderStats() {
  return useQuery({
    queryKey: orderKeys.stats(),
    queryFn: getUserOrderStats,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}
