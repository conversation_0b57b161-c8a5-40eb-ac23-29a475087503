import { Product } from '../products/types';

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parentId?: string;
  parent?: Category;
  children?: Category[];
  productCount?: number;
  featured?: boolean;
}

// API response types
export interface CategoriesApiResponse {
  categories: Category[];
  error?: string;
}

export interface CategoryApiResponse {
  category: Category | null;
  products?: Product[];
  error?: string;
}

export interface CategoryFilterParams {
  featured?: boolean;
  parentId?: string | null;
  withChildren?: boolean;
  withProductCount?: boolean;
}
