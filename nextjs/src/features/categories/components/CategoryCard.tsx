'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Category } from '../types';

interface CategoryCardProps {
  category: Category;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
}

export function CategoryCard({
  category,
  className = '',
  variant = 'default'
}: CategoryCardProps) {
  // Extract properties with fallbacks for different naming conventions
  const name = category.name;
  const slug = category.slug;
  const description = category.description;
  const productCount = category.productCount || (category as any).product_count;

  // Generate placeholder values for missing images
  const getInitial = () => name.charAt(0).toUpperCase();

  // Handle different image property names (database uses snake_case, our types use camelCase)
  let imageUrl = category.image ||
    (category as any).image_url ||
    (category as any).imageUrl ||
    (category as any).thumbnail ||
    '';

  // Add image transformation parameters for better performance
  if (imageUrl && imageUrl.includes('unsplash.com')) {
    imageUrl = `${imageUrl}&auto=format&fit=crop&w=800&q=80`;
  }

  console.log('CategoryCard props:', {
    name,
    slug,
    imageUrl,
    categoryData: JSON.stringify(category)
  });

  const hasImage = !!imageUrl;

  if (variant === 'compact') {
    return (
      <Link
        href={`/categories/${slug}`}
        className={`group flex items-center p-3 rounded-lg border border-gray-200 hover:border-primary-200 hover:bg-primary-50 transition-colors ${className}`}
      >
        <div className="relative w-10 h-10 rounded-full overflow-hidden bg-gray-100 mr-3 flex items-center justify-center">
          {hasImage ? (
            <Image
              src={imageUrl}
              alt={name}
              fill
              sizes="40px"
              className="object-cover"
            />
          ) : (
            <span className="text-sm font-bold text-gray-500">{getInitial()}</span>
          )}
        </div>
        <div>
          <h3 className="text-sm font-medium text-gray-900 group-hover:text-primary-600">{name}</h3>
          {productCount !== undefined && (
            <p className="text-xs text-gray-500">{productCount} products</p>
          )}
        </div>
      </Link>
    );
  }

  if (variant === 'featured') {
    return (
      <Link
        href={`/categories/${slug}`}
        className={`group block relative rounded-lg overflow-hidden ${className}`}
      >
        <div className="aspect-[16/9] relative bg-gradient-to-r from-blue-500 to-purple-600">
          {hasImage ? (
            <Image
              src={imageUrl}
              alt={name}
              fill
              sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-3xl font-bold text-white">{getInitial()}</span>
            </div>
          )}
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
        </div>
        <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
          <h3 className="text-lg font-bold">{name}</h3>
          {productCount !== undefined && (
            <p className="text-sm text-white/80">{productCount} products</p>
          )}
        </div>
      </Link>
    );
  }

  // Default variant
  return (
    <Link
      href={`/categories/${slug}`}
      className={`group block bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow ${className}`}
    >
      <div className="aspect-square relative bg-gradient-to-r from-blue-500 to-purple-600">
        {hasImage ? (
          <Image
            src={imageUrl}
            alt={name}
            fill
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
            className="object-cover transition-transform duration-300 group-hover:scale-105"
          />
        ) : (
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-3xl font-bold text-white">{getInitial()}</span>
          </div>
        )}
      </div>
      <div className="p-4">
        <h3 className="text-base font-medium text-gray-900 group-hover:text-primary-600">{name}</h3>
        {description && (
          <p className="mt-1 text-sm text-gray-500 line-clamp-2">{description}</p>
        )}
        {productCount !== undefined && (
          <p className="mt-2 text-sm font-medium text-primary-600">{productCount} products</p>
        )}
      </div>
    </Link>
  );
}
