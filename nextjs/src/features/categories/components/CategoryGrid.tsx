'use client';

import React from 'react';
import { CategoryCard } from './CategoryCard';
import { Category } from '../types';

interface CategoryGridProps {
  categories: Category[];
  className?: string;
  columns?: 2 | 3 | 4 | 5;
  variant?: 'default' | 'compact' | 'featured';
}

export function CategoryGrid({ 
  categories, 
  className = '',
  columns = 4,
  variant = 'default'
}: CategoryGridProps) {
  // Determine grid columns class based on the columns prop
  const gridColumnsClass = {
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4',
    5: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
  }[columns];

  if (!categories || categories.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">No categories found</p>
      </div>
    );
  }

  return (
    <div className={`grid ${gridColumnsClass} gap-4 md:gap-6 ${className}`}>
      {categories.map((category) => (
        <CategoryCard 
          key={category.id} 
          category={category} 
          variant={variant}
        />
      ))}
    </div>
  );
}
