'use client';

import { useQuery } from '@tanstack/react-query';
import {
  getCategoriesClient,
  getCategoryBySlugClient,
  getFeaturedCategoriesClient
} from './api-client';
import type { CategoryFilterParams } from './types';

// Query keys
export const categoryKeys = {
  all: ['categories'] as const,
  lists: () => [...categoryKeys.all, 'list'] as const,
  list: (filters: CategoryFilterParams) => [...categoryKeys.lists(), filters] as const,
  details: () => [...categoryKeys.all, 'detail'] as const,
  detail: (slug: string) => [...categoryKeys.details(), slug] as const,
  featured: () => [...categoryKeys.all, 'featured'] as const,
};

// Query hooks
export function useCategories(filters: CategoryFilterParams = {}) {
  return useQuery({
    queryKey: categoryKeys.list(filters),
    queryFn: () => getCategoriesClient(filters),
  });
}

export function useCategory(slug: string) {
  return useQuery({
    queryKey: categoryKeys.detail(slug),
    queryFn: () => getCategoryBySlugClient(slug),
    enabled: !!slug,
  });
}

export function useFeaturedCategories(limit = 6) {
  console.log('useFeaturedCategories hook called with limit:', limit);

  return useQuery({
    queryKey: [...categoryKeys.featured(), limit],
    queryFn: async () => {
      console.log('getFeaturedCategoriesClient function called with limit:', limit);
      const result = await getFeaturedCategoriesClient(limit);
      console.log('getFeaturedCategoriesClient result:', result);
      return result;
    },
  });
}
