import {
  Category,
  CategoryFilterParams,
  CategoriesApiResponse,
  CategoryApiResponse
} from './types';
import { createSPASassClient } from '@/lib/supabase/client';

// Client-side API functions
export async function getCategoriesClient(
  filters: CategoryFilterParams = {}
): Promise<CategoriesApiResponse> {
  try {
    // Try to fetch real data from the database
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    let query = supabase.from('categories').select('*');

    // Apply filters
    if (filters.featured !== undefined) {
      query = query.eq('featured', filters.featured);
    }

    if (filters.parentId !== undefined) {
      if (filters.parentId === null) {
        query = query.is('parent_id', null);
      } else {
        query = query.eq('parent_id', filters.parentId);
      }
    }

    if (filters.search) {
      query = query.ilike('name', `%${filters.search}%`);
    }

    // Apply sorting
    if (filters.sortBy) {
      query = query.order(filters.sortBy, { ascending: filters.sortOrder === 'asc' });
    } else {
      query = query.order('name');
    }

    const { data, error } = await query;

    if (error) throw error;

    return { categories: data as Category[] };
  } catch (error) {
    console.error('Error fetching categories:', error);
    return {
      categories: [],
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

export async function getCategoryBySlugClient(slug: string): Promise<CategoryApiResponse> {
  try {
    // Try to fetch real data from the database
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { data: categoryData, error: categoryError } = await supabase
      .from('categories')
      .select('*')
      .eq('slug', slug)
      .single();

    if (categoryError && categoryError.code !== 'PGRST116') { // PGRST116 is "No rows returned" error
      throw categoryError;
    }

    // If we found the category in the database
    if (categoryData) {
      // Get products for this category
      const { data: productsData, error: productsError } = await supabase
        .from('products')
        .select(`
          *,
          store:stores(*),
          images:product_images(*)
        `)
        .eq('category_id', categoryData.id)
        .limit(12);

      if (productsError) throw productsError;

      return {
        category: categoryData as Category,
        products: productsData || []
      };
    }

    // If no category found
    return {
      category: null,
      products: []
    };
  } catch (error) {
    console.error(`Error fetching category with slug ${slug}:`, error);
    return {
      category: null,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

export async function getFeaturedCategoriesClient(limit = 6): Promise<CategoriesApiResponse> {
  try {
    console.log('getFeaturedCategoriesClient called with limit:', limit);

    // Try to fetch real data from the database
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Get all categories, not just featured ones
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('name')
      .limit(limit);

    if (error) throw error;

    console.log('Categories from database:', data);

    if (data && data.length > 0) {
      return { categories: data as Category[] };
    }

    // Fallback to hardcoded categories if no data is found
    const hardcodedCategories: Category[] = [
      {
        id: 'cat-1',
        name: 'Electronics',
        slug: 'electronics',
        description: 'Latest gadgets and electronic devices',
        image: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
        featured: true,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      },
      {
        id: 'cat-2',
        name: 'Clothing',
        slug: 'clothing',
        description: 'Trendy fashion for all seasons',
        image: 'https://images.unsplash.com/photo-1567401893414-76b7b1e5a7a5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
        featured: true,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      },
      {
        id: 'cat-3',
        name: 'Home & Kitchen',
        slug: 'home-kitchen',
        description: 'Everything for your home',
        image: 'https://images.unsplash.com/photo-1583847268964-b28dc8f51f92?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80',
        featured: true,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      }
    ];

    console.log('Using hardcoded categories:', hardcodedCategories);
    return { categories: hardcodedCategories };
  } catch (error) {
    console.error('Error fetching categories:', error);
    return {
      categories: [],
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}
