'use client';

import React from 'react';
import { StoreCard } from './StoreCard';
import { Store } from '../types';

interface StoreGridProps {
  stores: Store[];
  className?: string;
  columns?: 2 | 3 | 4 | 5;
  variant?: 'default' | 'compact' | 'featured';
}

export function StoreGrid({
  stores,
  className = '',
  columns = 4,
  variant = 'default'
}: StoreGridProps) {
  console.log('StoreGrid component rendering with:', {
    storesCount: stores?.length || 0,
    variant,
    columns
  });

  // Determine grid columns class based on the columns prop
  const gridColumnsClass = {
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4',
    5: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
  }[columns];

  if (!stores || stores.length === 0) {
    console.log('StoreGrid: No stores found');
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">No stores found</p>
      </div>
    );
  }

  console.log('StoreGrid: Rendering stores:', stores);

  return (
    <div className={`grid ${gridColumnsClass} gap-4 md:gap-6 ${className}`}>
      {stores.map((store) => (
        <StoreCard
          key={store.id}
          store={store}
          variant={variant}
        />
      ))}
    </div>
  );
}
