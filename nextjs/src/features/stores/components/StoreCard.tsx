'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Star } from 'lucide-react';
import { Store } from '../types';

interface StoreCardProps {
  store: Store;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
}

export function StoreCard({
  store,
  className = '',
  variant = 'default'
}: StoreCardProps) {
  const {
    name,
    slug,
    logo,
    coverImage,
    description,
    rating,
    reviewCount,
    productCount
  } = store;

  // Generate placeholder values for missing images
  const getInitial = () => name.charAt(0).toUpperCase();

  // Handle different property names (database uses snake_case, our types use camelCase)
  const logoUrl = logo || (store as any).logo_url || (store as any).logoUrl || '';
  const coverUrl = coverImage || (store as any).cover_image || (store as any).coverImageUrl || '';



  if (variant === 'compact') {
    return (
      <Link
        href={`/stores/${slug}`}
        className={`group flex items-center p-3 rounded-lg border border-gray-200 hover:border-primary-200 hover:bg-primary-50 transition-colors ${className}`}
      >
        <div className="relative w-10 h-10 rounded-full overflow-hidden bg-gray-100 mr-3 flex items-center justify-center">
          {logoUrl ? (
            <Image
              src={logoUrl}
              alt={name}
              fill
              sizes="40px"
              className="object-cover"
            />
          ) : (
            <span className="text-sm font-bold text-gray-500">{getInitial()}</span>
          )}
        </div>
        <div>
          <h3 className="text-sm font-medium text-gray-900 group-hover:text-primary-600">{name}</h3>
          {productCount !== undefined && (
            <p className="text-xs text-gray-500">{productCount} products</p>
          )}
        </div>
      </Link>
    );
  }

  if (variant === 'featured') {
    return (
      <Link
        href={`/stores/${slug}`}
        className={`group block relative rounded-lg overflow-hidden ${className}`}
      >
        <div className="aspect-[16/9] relative bg-gradient-to-r from-blue-500 to-purple-600">
          {coverUrl ? (
            <Image
              src={coverUrl}
              alt={name}
              fill
              sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-3xl font-bold text-white">{getInitial()}</span>
            </div>
          )}
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
        </div>
        <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
          <div className="flex items-center mb-2">
            <div className="relative w-10 h-10 rounded-full overflow-hidden bg-white mr-3 flex items-center justify-center">
              {logoUrl ? (
                <Image
                  src={logoUrl}
                  alt={name}
                  fill
                  sizes="40px"
                  className="object-cover"
                />
              ) : (
                <span className="text-sm font-bold text-gray-500">{getInitial()}</span>
              )}
            </div>
            <h3 className="text-lg font-bold">{name}</h3>
          </div>
          {rating !== undefined && (
            <div className="flex items-center">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-3 h-3 ${
                      (i + 1) <= rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-xs ml-1 text-white/80">
                {rating.toFixed(1)} ({reviewCount} reviews)
              </span>
            </div>
          )}
        </div>
      </Link>
    );
  }

  // Default variant
  return (
    <Link
      href={`/stores/${slug}`}
      className={`group block bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow ${className}`}
    >
      {/* Cover image */}
      <div className="aspect-[3/1] relative bg-gradient-to-r from-blue-500 to-purple-600">
        {coverUrl ? (
          <Image
            src={coverUrl}
            alt={`${name} cover`}
            fill
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
            className="object-cover"
          />
        ) : (
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-3xl font-bold text-white">{getInitial()}</span>
          </div>
        )}
      </div>

      <div className="p-4">
        <div className="flex items-center -mt-8 mb-3">
          <div className="relative w-16 h-16 rounded-full overflow-hidden border-2 border-white bg-white flex items-center justify-center">
            {logoUrl ? (
              <Image
                src={logoUrl}
                alt={name}
                fill
                sizes="64px"
                className="object-cover"
              />
            ) : (
              <span className="text-lg font-bold text-gray-500">{getInitial()}</span>
            )}
          </div>
        </div>

        <h3 className="text-lg font-medium text-gray-900 group-hover:text-primary-600">{name}</h3>

        {rating !== undefined && (
          <div className="flex items-center mt-1">
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-3 h-3 ${
                    (i + 1) <= rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <span className="text-xs text-gray-500 ml-1">
              {rating.toFixed(1)} ({reviewCount} reviews)
            </span>
          </div>
        )}

        {description && (
          <p className="mt-2 text-sm text-gray-500 line-clamp-2">{description}</p>
        )}

        {productCount !== undefined && (
          <p className="mt-2 text-sm font-medium text-primary-600">{productCount} products</p>
        )}
      </div>
    </Link>
  );
}
