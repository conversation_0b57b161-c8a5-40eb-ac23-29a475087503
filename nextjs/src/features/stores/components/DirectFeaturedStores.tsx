'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRight } from 'lucide-react';

interface DirectFeaturedStoresProps {
  title: string;
  subtitle?: string;
  className?: string;
}

export function DirectFeaturedStores({
  title,
  subtitle,
  className = '',
}: DirectFeaturedStoresProps) {
  // Hardcoded store data
  const stores = [
    {
      id: 'store-1',
      name: 'TechHub',
      slug: 'techhub',
      description: 'Your one-stop shop for all things tech',
      logo: 'https://images.unsplash.com/photo-1535303311164-664fc9ec6532?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1734&q=80',
      coverImage: 'https://images.unsplash.com/photo-1478860409698-8707f313ee8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    },
    {
      id: 'store-2',
      name: 'Fashion Forward',
      slug: 'fashion-forward',
      description: 'Latest trends in fashion',
      logo: 'https://images.unsplash.com/photo-1507679799987-c73779587ccf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1742&q=80',
      coverImage: 'https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    },
    {
      id: 'store-3',
      name: 'Home Essentials',
      slug: 'home-essentials',
      description: 'Everything you need for your home',
      logo: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1558&q=80',
      coverImage: 'https://images.unsplash.com/photo-1484154218962-a197022b5858?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    },
    {
      id: 'store-4',
      name: 'Sports World',
      slug: 'sports-world',
      description: 'Gear for every sport',
      logo: 'https://images.unsplash.com/photo-1517649763962-0c623066013b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
      coverImage: 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    }
  ];

  return (
    <section className={`py-12 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div className="flex-1">
            <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
            {subtitle && <p className="mt-1 text-gray-500">{subtitle}</p>}
          </div>
          <Link
            href="/stores"
            className="mt-4 sm:mt-0 inline-flex items-center text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200 self-start sm:self-center"
          >
            View all
            <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
          {stores.map((store) => (
            <Link
              key={store.id}
              href={`/stores/${store.slug}`}
              className="group block relative rounded-lg overflow-hidden"
            >
              <div className="aspect-[16/9] relative bg-gradient-to-r from-blue-500 to-purple-600">
                {store.coverImage && (
                  <Image
                    src={store.coverImage}
                    alt={store.name}
                    fill
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
              </div>
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                <div className="flex items-center mb-2">
                  <div className="relative w-10 h-10 rounded-full overflow-hidden bg-white mr-3 flex items-center justify-center">
                    {store.logo ? (
                      <Image
                        src={store.logo}
                        alt={store.name}
                        fill
                        sizes="40px"
                        className="object-cover"
                      />
                    ) : (
                      <span className="text-sm font-bold text-gray-500">{store.name.charAt(0).toUpperCase()}</span>
                    )}
                  </div>
                  <h3 className="text-lg font-bold">{store.name}</h3>
                </div>
                {store.description && (
                  <p className="text-sm text-white/80 line-clamp-2">{store.description}</p>
                )}
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
