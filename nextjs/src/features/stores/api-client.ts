import {
  Store,
  StoreFilterParams,
  StoresApiResponse,
  StoreApiResponse
} from './types';
import { createSPASassClient } from '@/lib/supabase/client';

// Client-side API functions
export async function getStoresClient(
  filters: StoreFilterParams = {}
): Promise<StoresApiResponse> {
  try {
    console.log('getStoresClient called with filters:', filters);

    // Try to fetch real data from the database
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    let query = supabase.from('stores').select('*');

    // Apply filters
    if (filters.featured !== undefined) {
      query = query.eq('featured', filters.featured);
    }

    if (filters.search) {
      query = query.ilike('name', `%${filters.search}%`);
    }

    if (filters.sortBy) {
      query = query.order(filters.sortBy, { ascending: filters.sortOrder === 'asc' });
    } else {
      query = query.order('name');
    }

    const { data, error } = await query;

    if (error) throw error;

    // If we got data from the database, use it
    if (data && data.length > 0) {
      console.log('Using real data from database:', data);
      return { stores: data as Store[] };
    }

    // Hardcoded fallback stores with the correct property names
    const hardcodedStores: Store[] = [
      {
        id: 'store-1',
        name: 'TechHub',
        slug: 'techhub',
        description: 'Your one-stop shop for all things tech',
        logo: 'https://images.unsplash.com/photo-1535303311164-664fc9ec6532?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1734&q=80',
        coverImage: 'https://images.unsplash.com/photo-1478860409698-8707f313ee8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
        rating: 4.5,
        reviewCount: 120,
        productCount: 45,
        featured: true,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      },
      {
        id: 'store-2',
        name: 'Fashion Forward',
        slug: 'fashion-forward',
        description: 'Latest trends in fashion',
        logo: 'https://images.unsplash.com/photo-1507679799987-c73779587ccf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1742&q=80',
        coverImage: 'https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
        rating: 4.2,
        reviewCount: 85,
        productCount: 32,
        featured: true,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      },
      {
        id: 'store-3',
        name: 'Home Essentials',
        slug: 'home-essentials',
        description: 'Everything you need for your home',
        logo: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1558&q=80',
        coverImage: 'https://images.unsplash.com/photo-1484154218962-a197022b5858?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
        rating: 4.7,
        reviewCount: 150,
        productCount: 60,
        featured: true,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      },
      {
        id: 'store-5',
        name: 'Sports World',
        slug: 'sports-world',
        description: 'Gear for every sport',
        logo: 'https://images.unsplash.com/photo-1517649763962-0c623066013b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
        coverImage: 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
        rating: 4.3,
        reviewCount: 95,
        productCount: 40,
        featured: true,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      }
    ];

    // Filter the hardcoded stores based on the filters
    let filteredStores = [...hardcodedStores];

    if (filters.featured !== undefined) {
      filteredStores = filteredStores.filter(store => store.featured === filters.featured);
    }

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredStores = filteredStores.filter(store =>
        store.name.toLowerCase().includes(searchLower) ||
        (store.description && store.description.toLowerCase().includes(searchLower))
      );
    }

    console.log('Using hardcoded stores:', filteredStores);
    return { stores: filteredStores };
  } catch (error) {
    console.error('Error fetching stores:', error);
    return {
      stores: [],
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

export async function getStoreBySlugClient(slug: string): Promise<StoreApiResponse> {
  try {
    // Try to fetch real data from the database
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { data: storeData, error: storeError } = await supabase
      .from('stores')
      .select('*')
      .eq('slug', slug)
      .single();

    if (storeError && storeError.code !== 'PGRST116') { // PGRST116 is "No rows returned" error
      throw storeError;
    }

    // If we found the store in the database
    if (storeData) {
      // Get products for this store with images
      const { data: productsData, error: productsError } = await supabase
        .from('products')
        .select(`
          *,
          images:product_images(*)
        `)
        .eq('store_id', storeData.id)
        .limit(12);

      if (productsError) throw productsError;

      // Transform products data to match the expected Product type
      const transformedProducts = (productsData || []).map(product => ({
        id: product.id,
        name: product.name,
        slug: product.slug,
        description: product.description,
        price: product.price,
        compareAtPrice: product.compare_at_price,
        currency: product.currency || 'GMD',
        images: (product.images || []).map((img: any) => ({
          id: img.id,
          productId: img.product_id,
          url: img.url,
          alt: img.alt || product.name,
          position: img.position
        })),
        categoryId: product.category_id,
        storeId: product.store_id,
        featured: product.featured,
        trending: product.trending || false,
        inStock: product.in_stock !== false, // Default to true if not specified
        rating: product.rating || 0,
        reviewCount: product.review_count || 0,
        createdAt: product.created_at,
        updatedAt: product.updated_at || product.created_at
      }));

      return {
        store: storeData as Store,
        products: transformedProducts
      };
    }

    // No store found in database
    return null;


  } catch (error) {
    console.error(`Error fetching store with slug ${slug}:`, error);
    return {
      store: null,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

export async function getFeaturedStoresClient(limit = 4): Promise<StoresApiResponse> {
  try {
    // Try to fetch real data from the database
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { data, error } = await supabase
      .from('stores')
      .select('*')
      .eq('featured', true)
      .order('name')
      .limit(limit);

    if (error) throw error;

    // If we got data from the database, use it
    if (data && data.length > 0) {
      return { stores: data as Store[] };
    }

    // Hardcoded fallback stores with the correct property names
    const hardcodedStores: Store[] = [
      {
        id: 'store-1',
        name: 'TechHub',
        slug: 'techhub',
        description: 'Your one-stop shop for all things tech',
        logo: 'https://images.unsplash.com/photo-1535303311164-664fc9ec6532?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1734&q=80',
        coverImage: 'https://images.unsplash.com/photo-1478860409698-8707f313ee8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
        rating: 4.5,
        reviewCount: 120,
        productCount: 45,
        featured: true,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      },
      {
        id: 'store-2',
        name: 'Fashion Forward',
        slug: 'fashion-forward',
        description: 'Latest trends in fashion',
        logo: 'https://images.unsplash.com/photo-1507679799987-c73779587ccf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1742&q=80',
        coverImage: 'https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
        rating: 4.2,
        reviewCount: 85,
        productCount: 32,
        featured: true,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      },
      {
        id: 'store-3',
        name: 'Home Essentials',
        slug: 'home-essentials',
        description: 'Everything you need for your home',
        logo: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1558&q=80',
        coverImage: 'https://images.unsplash.com/photo-1484154218962-a197022b5858?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
        rating: 4.7,
        reviewCount: 150,
        productCount: 60,
        featured: true,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      },
      {
        id: 'store-5',
        name: 'Sports World',
        slug: 'sports-world',
        description: 'Gear for every sport',
        logo: 'https://images.unsplash.com/photo-1517649763962-0c623066013b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
        coverImage: 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
        rating: 4.3,
        reviewCount: 95,
        productCount: 40,
        featured: true,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z'
      }
    ];

    console.log('Using hardcoded featured stores:', hardcodedStores);
    return { stores: hardcodedStores.slice(0, limit) };
  } catch (error) {
    console.error('Error fetching featured stores:', error);
    return {
      stores: [],
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}
