import { createSPASassClient } from '@/lib/supabase/client';
import { StorageFile, StorageFolder, StorageItem, UploadFileParams, StorageError } from './types';

export async function listFiles(path: string = ''): Promise<{ items: StorageItem[], error: StorageError | null }> {
  try {
    const client = await createSPASassClient();
    const { data, error } = await client.storage.from('files').list(path);
    
    if (error) {
      return { items: [], error: { message: error.message } };
    }
    
    // Transform the data into our StorageItem format
    const items: StorageItem[] = [];
    
    // Add folders
    const folders = data.filter(item => item.metadata === null);
    folders.forEach(folder => {
      items.push({
        id: folder.id,
        name: folder.name,
        created_at: folder.created_at,
        path: path ? `${path}/${folder.name}` : folder.name,
        isFolder: true
      });
    });
    
    // Add files
    const files = data.filter(item => item.metadata !== null);
    files.forEach(file => {
      items.push({
        id: file.id,
        name: file.name,
        size: file.metadata?.size,
        type: file.metadata?.mimetype,
        created_at: file.created_at,
        updated_at: file.updated_at,
        path: path ? `${path}/${file.name}` : file.name,
        isFolder: false
      });
    });
    
    return { items, error: null };
  } catch (error) {
    return { 
      items: [], 
      error: { 
        message: error instanceof Error ? error.message : 'An unknown error occurred while listing files'
      } 
    };
  }
}

export async function uploadFile({ file, path = '', onProgress }: UploadFileParams): Promise<{ file: StorageFile | null, error: StorageError | null }> {
  try {
    const client = await createSPASassClient();
    
    const filePath = path ? `${path}/${file.name}` : file.name;
    
    const { data, error } = await client.storage.from('files').upload(filePath, file, {
      cacheControl: '3600',
      upsert: true,
      onUploadProgress: onProgress ? (progress) => {
        const percentage = (progress.loaded / progress.total) * 100;
        onProgress(percentage);
      } : undefined
    });
    
    if (error) {
      return { file: null, error: { message: error.message } };
    }
    
    // Get the URL for the uploaded file
    const { data: urlData } = await client.storage.from('files').getPublicUrl(filePath);
    
    const uploadedFile: StorageFile = {
      id: data.path,
      name: file.name,
      size: file.size,
      type: file.type,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      path: data.path,
      url: urlData.publicUrl
    };
    
    return { file: uploadedFile, error: null };
  } catch (error) {
    return { 
      file: null, 
      error: { 
        message: error instanceof Error ? error.message : 'An unknown error occurred during file upload'
      } 
    };
  }
}

export async function deleteFile(path: string): Promise<{ success: boolean, error: StorageError | null }> {
  try {
    const client = await createSPASassClient();
    const { error } = await client.storage.from('files').remove([path]);
    
    if (error) {
      return { success: false, error: { message: error.message } };
    }
    
    return { success: true, error: null };
  } catch (error) {
    return { 
      success: false, 
      error: { 
        message: error instanceof Error ? error.message : 'An unknown error occurred while deleting the file'
      } 
    };
  }
}

export async function createFolder(path: string, folderName: string): Promise<{ folder: StorageFolder | null, error: StorageError | null }> {
  try {
    const client = await createSPASassClient();
    
    // Supabase doesn't have a direct "create folder" API, so we create an empty file with a folder path
    const folderPath = path ? `${path}/${folderName}/.folder` : `${folderName}/.folder`;
    
    const { error } = await client.storage.from('files').upload(folderPath, new Blob(['']), {
      cacheControl: '3600',
      upsert: true
    });
    
    if (error) {
      return { folder: null, error: { message: error.message } };
    }
    
    const newFolder: StorageFolder = {
      id: folderName,
      name: folderName,
      path: path ? `${path}/${folderName}` : folderName,
      created_at: new Date().toISOString()
    };
    
    return { folder: newFolder, error: null };
  } catch (error) {
    return { 
      folder: null, 
      error: { 
        message: error instanceof Error ? error.message : 'An unknown error occurred while creating the folder'
      } 
    };
  }
}

export async function getFileUrl(path: string): Promise<{ url: string | null, error: StorageError | null }> {
  try {
    const client = await createSPASassClient();
    const { data, error } = await client.storage.from('files').getPublicUrl(path);
    
    if (error) {
      return { url: null, error: { message: error.message } };
    }
    
    return { url: data.publicUrl, error: null };
  } catch (error) {
    return { 
      url: null, 
      error: { 
        message: error instanceof Error ? error.message : 'An unknown error occurred while getting the file URL'
      } 
    };
  }
}
