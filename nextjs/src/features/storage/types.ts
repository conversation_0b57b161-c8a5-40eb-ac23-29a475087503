export interface StorageFile {
  id: string;
  name: string;
  size: number;
  type: string;
  created_at: string;
  updated_at: string;
  path: string;
  url?: string;
}

export interface StorageFolder {
  id: string;
  name: string;
  path: string;
  created_at: string;
}

export interface StorageItem {
  id: string;
  name: string;
  size?: number;
  type?: string;
  created_at: string;
  updated_at?: string;
  path: string;
  url?: string;
  isFolder: boolean;
}

export interface UploadFileParams {
  file: File;
  path?: string;
  onProgress?: (progress: number) => void;
}

export interface StorageError {
  message: string;
  status?: number;
}
