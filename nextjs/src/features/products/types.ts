// Shipping types
export type ShippingType = 'local' | 'international';

export interface DeliveryService {
  id: string;
  name: string;
  description?: string;
  estimatedDeliveryTime: string;
  cost: number;
  isActive: boolean;
}

export interface CourierService {
  id: string;
  name: string;
  description?: string;
  estimatedDeliveryTime: string;
  baseCost: number; // Base cost for shipping
  costPerKg?: number; // Additional cost per kg (for weight-based pricing)
  freeWeightLimit?: number; // Weight limit (kg) before additional charges apply
  trackingSupported: boolean;
  countries: string[];
  isActive: boolean;
}

export interface ProductShipping {
  shippingType: ShippingType;
  deliveryServiceId?: string;
  courierServiceId?: string;
  deliveryService?: DeliveryService;
  courierService?: CourierService;
}

// Product types
export interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  price: number;
  compareAtPrice?: number;
  currency: string;
  weight?: number; // Weight in kg for shipping calculations
  images: ProductImage[];
  category?: Category;
  categoryId?: string;
  store?: Store;
  storeId?: string;
  featured: boolean;
  trending: boolean;
  inStock: boolean;
  isLocal: boolean; // Indicates whether the product is local or international
  rating: number;
  reviewCount: number;
  createdAt: string;
  updatedAt: string;
  specifications?: ProductSpecification[];
  shipping?: ProductShipping;
}

export interface ProductImage {
  id: string;
  productId: string;
  url: string;
  alt: string;
  position: number;
}

export interface ProductSpecification {
  id: string;
  productId: string;
  name: string;
  value: string;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parentId?: string;
  parent?: Category;
  children?: Category[];
  productCount?: number;
  featured?: boolean;
}

export interface Store {
  id: string;
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  coverImage?: string;
  rating?: number;
  reviewCount?: number;
  productCount?: number;
  featured?: boolean;
}

// Pagination and filtering types
export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface ProductFilterParams {
  categoryId?: string;
  storeId?: string;
  featured?: boolean;
  trending?: boolean;
  inStock?: boolean;
  minPrice?: number;
  maxPrice?: number;
  minWeight?: number;
  maxWeight?: number;
  search?: string;
  sortBy?: keyof Product;
  sortOrder?: 'asc' | 'desc';
  shippingType?: ShippingType;
  deliveryServiceId?: string;
  courierServiceId?: string;
}

// API response types
export interface ProductsApiResponse {
  products: PaginatedResponse<Product>;
  error?: string;
}

export interface ProductApiResponse {
  product: Product | null;
  error?: string;
}

export interface CategoriesApiResponse {
  categories: Category[];
  error?: string;
}

export interface CategoryApiResponse {
  category: Category | null;
  error?: string;
}

export interface DeliveryServicesApiResponse {
  deliveryServices: DeliveryService[];
  error?: string;
}

export interface CourierServicesApiResponse {
  courierServices: CourierService[];
  error?: string;
}
