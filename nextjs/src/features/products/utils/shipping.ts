import { DeliveryService, CourierService } from '../types';

/**
 * Calculate shipping cost based on weight and service pricing
 */
export function calculateShippingCost(
  service: DeliveryService | CourierService,
  productWeight?: number
): number {
  // For delivery services (local), weight doesn't affect cost - use base_fee
  if ('baseFee' in service || 'base_fee' in service) {
    // Handle both camelCase and snake_case from database
    return (service as any).baseFee || (service as any).base_fee || 0;
  }

  // For delivery services with 'cost' field (legacy)
  if ('cost' in service) {
    return service.cost;
  }

  // For courier services (international), calculate based on weight
  const courierService = service as CourierService;
  const weight = productWeight || 0;
  
  let totalCost = courierService.baseCost;

  // Apply weight-based pricing if applicable
  if (courierService.costPerKg && courierService.freeWeightLimit !== undefined) {
    const chargeableWeight = Math.max(0, weight - courierService.freeWeightLimit);
    if (chargeableWeight > 0) {
      totalCost += chargeableWeight * courierService.costPerKg;
    }
  }

  return totalCost;
}