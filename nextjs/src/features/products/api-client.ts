import {
  PaginationParams,
  ProductFilterParams,
  ProductsApiResponse,
  ProductApiResponse,
  DeliveryServicesApiResponse,
  CourierServicesApiResponse
} from './types';
import { createSPASassClient } from '@/lib/supabase/client';

// Client-side API functions
export async function getProductsClient(
  paginationParams: PaginationParams = { page: 1, limit: 10 },
  filterParams: ProductFilterParams = {}
): Promise<ProductsApiResponse> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Calculate pagination
    const { page, limit } = paginationParams;
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    // Build query
    let query = supabase
      .from('products')
      .select(`
        *,
        category:categories(*),
        store:stores(*),
        images:product_images(*),
        shipping:product_shipping(
          *,
          delivery_service:external_delivery_services(*),
          courier_service:courier_services(*)
        )
      `, { count: 'exact' })
      .range(from, to);

    // Apply filters
    if (filterParams.categoryId) {
      query = query.eq('category_id', filterParams.categoryId);
    }

    if (filterParams.storeId) {
      query = query.eq('store_id', filterParams.storeId);
    }

    if (filterParams.featured !== undefined) {
      query = query.eq('featured', filterParams.featured);
    }

    if (filterParams.trending !== undefined) {
      query = query.eq('trending', filterParams.trending);
    }

    if (filterParams.inStock !== undefined) {
      query = query.eq('in_stock', filterParams.inStock);
    }

    if (filterParams.search) {
      query = query.ilike('name', `%${filterParams.search}%`);
    }

    if (filterParams.minPrice !== undefined) {
      query = query.gte('price', filterParams.minPrice);
    }

    if (filterParams.maxPrice !== undefined) {
      query = query.lte('price', filterParams.maxPrice);
    }

    if (filterParams.minWeight !== undefined) {
      query = query.gte('weight', filterParams.minWeight);
    }

    if (filterParams.maxWeight !== undefined) {
      query = query.lte('weight', filterParams.maxWeight);
    }

    // Apply shipping filters
    if (filterParams.shippingType) {
      query = query.eq('product_shipping.shipping_type', filterParams.shippingType);
    }

    if (filterParams.deliveryServiceId) {
      query = query.eq('product_shipping.delivery_service_id', filterParams.deliveryServiceId);
    }

    if (filterParams.courierServiceId) {
      query = query.eq('product_shipping.courier_service_id', filterParams.courierServiceId);
    }

    // Apply sorting
    const sortBy = filterParams.sortBy || 'created_at';
    const sortOrder = filterParams.sortOrder || 'desc';
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Execute query
    const { data, error, count } = await query;

    if (error) throw error;

    // Format the response
    const totalPages = Math.ceil((count || 0) / limit);

    // Transform data to ensure inStock and isLocal are properly set
    const transformedData = (data || []).map(product => {
      // Debug logging
      console.log('Product debug:', {
        id: product.id,
        name: product.name,
        is_local_from_db: product.is_local,
        shipping_data: product.shipping,
        shipping_length: product.shipping?.length || 0
      });

      return {
        ...product,
        // Always set products as in stock
        inStock: true,
        // Use is_local field from database directly instead of shipping table
        isLocal: product.is_local ?? true // Use database field, default to local if null
      };
    });

    return {
      products: {
        data: transformedData,
        meta: {
          total: count || 0,
          page,
          limit,
          totalPages
        }
      }
    };
  } catch (error) {
    console.error('Error fetching products:', error);
    return {
      products: {
        data: [],
        meta: {
          total: 0,
          page: paginationParams.page,
          limit: paginationParams.limit,
          totalPages: 0
        }
      },
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

export async function getProductBySlugClient(slug: string): Promise<ProductApiResponse> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Get product by slug
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        category:categories(*),
        store:stores(*),
        images:product_images(*),
        shipping:product_shipping(
          *,
          delivery_service:external_delivery_services(*),
          courier_service:courier_services(*)
        )
      `)
      .eq('slug', slug)
      .single();

    if (error) throw error;

    // Transform data to ensure inStock and isLocal are properly set
    if (data) {
      // Debug logging
      console.log('Single Product debug:', {
        id: data.id,
        name: data.name,
        is_local_from_db: data.is_local,
        shipping_data: data.shipping,
        shipping_length: data.shipping?.length || 0
      });

      const transformedProduct = {
        ...data,
        // Always set products as in stock
        inStock: true,
        // Use is_local field from database directly instead of shipping table
        isLocal: data.is_local ?? true // Use database field, default to local if null
      };
      return { product: transformedProduct };
    }

    return { product: null };
  } catch (error) {
    console.error(`Error fetching product with slug ${slug}:`, error);
    return {
      product: null,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

export async function getFeaturedProductsClient(limit = 8): Promise<ProductsApiResponse> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Directly query for featured products to ensure we get all the data we need
    const { data, error, count } = await supabase
      .from('products')
      .select(`
        *,
        category:categories(*),
        store:stores(*),
        images:product_images(*),
        shipping:product_shipping(
          *,
          delivery_service:external_delivery_services(*),
          courier_service:courier_services(*)
        )
      `, { count: 'exact' })
      .eq('featured', true)
      .limit(limit);

    if (error) throw error;

    console.log('Featured products data:', {
      count,
      productsCount: data?.length,
      firstProduct: data?.[0] ? {
        id: data[0].id,
        name: data[0].name,
        hasImages: data[0].images && data[0].images.length > 0,
        imageUrl: data[0].image_url,
        imagesCount: data[0].images?.length
      } : null
    });

    // Transform data to ensure inStock and isLocal are properly set
    const transformedData = (data || []).map(product => {
      // Debug logging
      console.log('Featured Product debug:', {
        id: product.id,
        name: product.name,
        is_local_from_db: product.is_local,
        shipping_data: product.shipping,
        shipping_length: product.shipping?.length || 0
      });

      return {
        ...product,
        // Always set products as in stock
        inStock: true,
        // Use is_local field from database directly instead of shipping table
        isLocal: product.is_local ?? true // Use database field, default to local if null
      };
    });

    return {
      products: {
        data: transformedData,
        meta: {
          total: count || 0,
          page: 1,
          limit,
          totalPages: Math.ceil((count || 0) / limit)
        }
      }
    };
  } catch (error) {
    console.error('Error fetching featured products:', error);
    return {
      products: {
        data: [],
        meta: {
          total: 0,
          page: 1,
          limit,
          totalPages: 0
        }
      },
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

// Get delivery services
export async function getDeliveryServicesClient(): Promise<DeliveryServicesApiResponse> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { data, error } = await supabase
      .from('external_delivery_services')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) throw error;

    return {
      deliveryServices: data || []
    };
  } catch (error) {
    console.error('Error fetching delivery services:', error);
    return {
      deliveryServices: [],
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

// Get courier services
export async function getCourierServicesClient(): Promise<CourierServicesApiResponse> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { data, error } = await supabase
      .from('courier_services')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) throw error;

    return {
      courierServices: data || []
    };
  } catch (error) {
    console.error('Error fetching courier services:', error);
    return {
      courierServices: [],
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}
