'use client';

import { useState } from 'react';
import {
  ShippingType,
  ProductShipping
} from '../types';
import { useDeliveryServices, useCourierServices } from '../queries';
import { calculateShippingCost } from '../utils/shipping';

interface ShippingSelectorProps {
  currentShipping?: ProductShipping;
  onShippingChange: (shipping: ProductShipping) => void;
  productWeight?: number; // Weight in kg for cost calculation
  className?: string;
}

export function ShippingSelector({ 
  currentShipping, 
  onShippingChange, 
  productWeight,
  className = '' 
}: ShippingSelectorProps) {
  const [shippingType, setShippingType] = useState<ShippingType>(
    currentShipping?.shippingType || 'local'
  );
  const [selectedDeliveryService, setSelectedDeliveryService] = useState<string>(
    currentShipping?.deliveryServiceId || ''
  );
  const [selectedCourierService, setSelectedCourierService] = useState<string>(
    currentShipping?.courierServiceId || ''
  );

  const { data: deliveryServices, isLoading: isLoadingDelivery } = useDeliveryServices();
  const { data: courierServices, isLoading: isLoadingCourier } = useCourierServices();

  const handleShippingTypeChange = (type: ShippingType) => {
    setShippingType(type);
    
    // Reset selections when changing type
    setSelectedDeliveryService('');
    setSelectedCourierService('');
    
    // Update parent component
    onShippingChange({
      shippingType: type,
      deliveryServiceId: type === 'local' ? undefined : undefined,
      courierServiceId: type === 'international' ? undefined : undefined,
    });
  };

  const handleServiceChange = (serviceId: string, serviceType: 'delivery' | 'courier') => {
    if (serviceType === 'delivery') {
      setSelectedDeliveryService(serviceId);
      const service = deliveryServices?.deliveryServices.find(s => s.id === serviceId);
      onShippingChange({
        shippingType,
        deliveryServiceId: serviceId,
        deliveryService: service,
      });
    } else {
      setSelectedCourierService(serviceId);
      const service = courierServices?.courierServices.find(s => s.id === serviceId);
      onShippingChange({
        shippingType,
        courierServiceId: serviceId,
        courierService: service,
      });
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Shipping Type
        </label>
        <div className="flex space-x-4">
          <label className="flex items-center">
            <input
              type="radio"
              name="shippingType"
              value="local"
              checked={shippingType === 'local'}
              onChange={() => handleShippingTypeChange('local')}
              className="mr-2"
            />
            Local Delivery
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="shippingType"
              value="international"
              checked={shippingType === 'international'}
              onChange={() => handleShippingTypeChange('international')}
              className="mr-2"
            />
            International Shipping
          </label>
        </div>
      </div>

      {shippingType === 'local' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Delivery Service
          </label>
          {isLoadingDelivery ? (
            <div className="text-gray-500">Loading delivery services...</div>
          ) : (
            <select
              value={selectedDeliveryService}
              onChange={(e) => handleServiceChange(e.target.value, 'delivery')}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Choose a delivery service</option>
              {deliveryServices?.deliveryServices.map((service) => (
                <option key={service.id} value={service.id}>
                  {service.name} - ${service.cost} ({service.estimatedDeliveryTime})
                </option>
              ))}
            </select>
          )}
        </div>
      )}

      {shippingType === 'international' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Courier Service
          </label>
          {isLoadingCourier ? (
            <div className="text-gray-500">Loading courier services...</div>
          ) : (
            <select
              value={selectedCourierService}
              onChange={(e) => handleServiceChange(e.target.value, 'courier')}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Choose a courier service</option>
              {courierServices?.courierServices.map((service) => (
                <option key={service.id} value={service.id}>
                  {service.name} - ${calculateShippingCost(service, productWeight).toFixed(2)} ({service.estimatedDeliveryTime})
                  {service.trackingSupported && ' • Tracking Available'}
                </option>
              ))}
            </select>
          )}
        </div>
      )}

      {/* Display selected service information */}
      {shippingType === 'local' && selectedDeliveryService && (
        <div className="p-3 bg-blue-50 rounded-md">
          {(() => {
            const service = deliveryServices?.deliveryServices.find(s => s.id === selectedDeliveryService);
            return service ? (
              <div>
                <h4 className="font-medium text-blue-900">{service.name}</h4>
                <p className="text-sm text-blue-700">
                  Delivery time: {service.estimatedDeliveryTime}
                </p>
                <p className="text-sm text-blue-700">Cost: ${service.cost}</p>
                {service.description && (
                  <p className="text-sm text-blue-600 mt-1">{service.description}</p>
                )}
              </div>
            ) : null;
          })()}
        </div>
      )}

      {shippingType === 'international' && selectedCourierService && (
        <div className="p-3 bg-green-50 rounded-md">
          {(() => {
            const service = courierServices?.courierServices.find(s => s.id === selectedCourierService);
            return service ? (
              <div>
                <h4 className="font-medium text-green-900">{service.name}</h4>
                <p className="text-sm text-green-700">
                  Delivery time: {service.estimatedDeliveryTime}
                </p>
                <p className="text-sm text-green-700">Cost: ${calculateShippingCost(service, productWeight).toFixed(2)}</p>
                {service.trackingSupported && (
                  <p className="text-sm text-green-600">✓ Tracking supported</p>
                )}
                <p className="text-sm text-green-600">
                  Ships to: {service.countries.join(', ')}
                </p>
                {service.description && (
                  <p className="text-sm text-green-600 mt-1">{service.description}</p>
                )}
              </div>
            ) : null;
          })()}
        </div>
      )}
    </div>
  );
}