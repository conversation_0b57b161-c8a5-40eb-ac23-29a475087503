/**
 * User-related utilities
 */

import { User } from '@supabase/supabase-js';

/**
 * Get user initials from email or name
 * @param user - The user object
 * @returns User initials
 */
export function getUserInitials(user: User | null): string {
  if (!user) return '??';
  
  // If user has a name, use that
  if (user.user_metadata?.full_name) {
    const nameParts = user.user_metadata.full_name.split(' ');
    if (nameParts.length > 1) {
      return (nameParts[0][0] + nameParts[1][0]).toUpperCase();
    }
    return nameParts[0].slice(0, 2).toUpperCase();
  }
  
  // Otherwise use email
  if (user.email) {
    const parts = user.email.split('@')[0].split(/[._-]/);
    return parts.length > 1
      ? (parts[0][0] + parts[1][0]).toUpperCase()
      : parts[0].slice(0, 2).toUpperCase();
  }
  
  // Fallback
  return '??';
}

/**
 * Get user display name
 * @param user - The user object
 * @returns User display name
 */
export function getUserDisplayName(user: User | null): string {
  if (!user) return 'Guest';
  
  if (user.user_metadata?.full_name) {
    return user.user_metadata.full_name;
  }
  
  if (user.email) {
    return user.email.split('@')[0];
  }
  
  return 'User';
}

/**
 * Get user avatar URL
 * @param user - The user object
 * @returns User avatar URL or null
 */
export function getUserAvatar(user: User | null): string | null {
  if (!user) return null;
  
  if (user.user_metadata?.avatar_url) {
    return user.user_metadata.avatar_url;
  }
  
  return null;
}

/**
 * Check if user has a specific role
 * @param user - The user object
 * @param role - The role to check
 * @returns Whether the user has the role
 */
export function hasRole(user: User | null, role: string): boolean {
  if (!user) return false;
  
  const roles = user.app_metadata?.roles || [];
  return Array.isArray(roles) ? roles.includes(role) : roles === role;
}

/**
 * Get user roles
 * @param user - The user object
 * @returns Array of user roles
 */
export function getUserRoles(user: User | null): string[] {
  if (!user) return [];
  
  const roles = user.app_metadata?.roles || [];
  return Array.isArray(roles) ? roles : [roles].filter(Boolean);
}