/**
 * Currency formatting utilities
 */

/**
 * Format a number as currency
 * @param amount - The amount to format
 * @param currency - The currency code (default: 'GMD')
 * @param options - Formatting options
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number,
  currency: string = 'GMD',
  options: {
    notation?: Intl.NumberFormatOptions['notation'];
    maximumFractionDigits?: number;
    minimumFractionDigits?: number;
  } = {}
): string {
  const {
    notation = 'standard',
    maximumFractionDigits = 2,
    minimumFractionDigits = 2,
  } = options;

  // For GMD (Dalasis), use the custom formatter
  if (currency === 'GMD') {
    return new Intl.NumberFormat('en-GM', {
      style: 'currency',
      currency: 'GMD',
      notation,
      maximumFractionDigits,
      minimumFractionDigits,
    }).format(amount);
  }

  // For other currencies, use their respective formatters
  return new Intl.NumberFormat(undefined, {
    style: 'currency',
    currency,
    notation,
    maximumFractionDigits,
    minimumFractionDigits,
  }).format(amount);
}

/**
 * Calculate discount percentage between original price and sale price
 * @param originalPrice - The original price
 * @param salePrice - The sale price
 * @returns Discount percentage as a whole number
 */
export function calculateDiscountPercentage(
  originalPrice: number,
  salePrice: number
): number {
  if (!originalPrice || !salePrice || originalPrice <= 0) return 0;
  return Math.round(((originalPrice - salePrice) / originalPrice) * 100);
}

/**
 * Format a percentage
 * @param value - The percentage value
 * @param decimals - Number of decimal places
 * @returns Formatted percentage string
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`;
}