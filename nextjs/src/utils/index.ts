/**
 * Utilities index file
 * 
 * This file re-exports all utilities from the various utility modules
 * to provide a single import point for common utilities.
 */

import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

// UI utility for merging class names with Tailwind
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Re-export all formatting utilities
export * from './formatting/currency';
export * from './formatting/date';
export * from './formatting/number';

// Re-export all string utilities
export * from './string';

// Re-export all user utilities
export * from './user';