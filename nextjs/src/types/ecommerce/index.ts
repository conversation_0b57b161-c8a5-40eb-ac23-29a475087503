/**
 * Consolidated ecommerce types
 * 
 * This file contains all the types related to the ecommerce functionality.
 * It merges and standardizes types from multiple sources to create a single source of truth.
 */

// Product types
export interface Product {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  price: number;
  compareAtPrice?: number | null;
  currency: string;
  images?: ProductImage[];
  category?: Category;
  categoryId?: string | null;
  store?: Store;
  storeId?: string | null;
  featured: boolean;
  trending?: boolean;
  inStock: boolean;
  isLocal?: boolean; // Indicates whether the product is local or international
  rating?: number | null;
  reviewCount?: number | null;
  createdAt: string;
  updatedAt: string;
  specifications?: ProductSpecification[];
}

export interface ProductImage {
  id: string;
  productId: string;
  url: string;
  alt: string | null;
  position: number;
  createdAt?: string;
}

export interface ProductSpecification {
  id: string;
  productId: string;
  name: string;
  value: string;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  imageUrl?: string | null;
  parentId?: string | null;
  parent?: Category;
  children?: Category[];
  productCount?: number;
  featured?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface Store {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  logo?: string | null;
  coverImage?: string | null;
  rating?: number | null;
  reviewCount?: number | null;
  productCount?: number;
  featured?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Order types
export interface Order {
  id: string;
  userId: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  subtotal: number;
  serviceFee?: number | null;
  deliveryFee?: number | null;
  total: number;
  currency: string;
  deliveryMethod?: 'delivery' | 'pickup' | null;
  shippingAddress?: Address | null;
  billingAddress?: Address | null;
  paymentIntentId?: string | null;
  shippingMethod?: string | null;
  trackingNumber?: string | null;
  notes?: string | null;
  selectedDeliveryServiceId?: string | null;
  items?: OrderItem[];
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  quantity: number;
  price: number;
  name: string;
  options?: Record<string, any> | null;
  product?: Product;
  createdAt: string;
}

// Cart types
export interface CartItem {
  id: string;
  userId: string;
  productId: string;
  quantity: number;
  options?: Record<string, any> | null;
  product?: Product;
  createdAt: string;
  updatedAt?: string;
}

// Review types
export interface Review {
  id: string;
  userId: string;
  productId?: string;
  storeId?: string;
  rating: number;
  title?: string | null;
  content?: string | null;
  status: 'pending' | 'approved' | 'rejected';
  user?: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
  };
  createdAt: string;
  updatedAt?: string;
}

// Wishlist types
export interface WishlistItem {
  id: string;
  userId: string;
  productId: string;
  product?: Product;
  createdAt: string;
}

// Address types
export interface Address {
  firstName: string;
  lastName: string;
  addressLine1: string;
  addressLine2?: string | null;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string | null;
}

// Delivery service types
export interface DeliveryService {
  id: string;
  name: string;
  description?: string | null;
  logo?: string | null;
  contactPhone?: string | null;
  contactEmail?: string | null;
  website?: string | null;
  baseFee: number;
  perKmFee?: number | null;
  minimumOrderAmount?: number | null;
  maximumDeliveryRadiusKm?: number | null;
  estimatedDeliveryTime?: string | null;
  isActive: boolean;
  coverageAreas?: any[] | null;
  operatingHours?: Record<string, any> | null;
  createdAt: string;
  updatedAt: string;
}

// Pagination and filtering types
export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface ProductFilterParams {
  categoryId?: string;
  storeId?: string;
  featured?: boolean;
  trending?: boolean;
  inStock?: boolean;
  minPrice?: number;
  maxPrice?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// API response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
}

export interface ProductsApiResponse extends ApiResponse<PaginatedResponse<Product>> {}
export interface ProductApiResponse extends ApiResponse<Product> {}
export interface CategoriesApiResponse extends ApiResponse<Category[]> {}
export interface CategoryApiResponse extends ApiResponse<Category> {}
export interface StoresApiResponse extends ApiResponse<PaginatedResponse<Store>> {}
export interface StoreApiResponse extends ApiResponse<Store> {}
export interface OrdersApiResponse extends ApiResponse<PaginatedResponse<Order>> {}
export interface OrderApiResponse extends ApiResponse<Order> {}