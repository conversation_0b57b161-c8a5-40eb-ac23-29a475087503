/**
 * Format a number as currency
 * @param amount - The amount to format
 * @param currency - The currency code (default: 'GMD')
 * @param options - Formatting options
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number,
  currency: string = 'GMD',
  options: {
    notation?: Intl.NumberFormatOptions['notation'];
    maximumFractionDigits?: number;
    minimumFractionDigits?: number;
  } = {}
): string {
  const {
    notation = 'standard',
    maximumFractionDigits = 2,
    minimumFractionDigits = 2,
  } = options;

  // For GMD (Dalasis), use the custom formatter
  if (currency === 'GMD') {
    return new Intl.NumberFormat('en-GM', {
      style: 'currency',
      currency: 'GMD',
      notation,
      maximumFractionDigits,
      minimumFractionDigits,
    }).format(amount);
  }

  // For other currencies, use their respective formatters
  return new Intl.NumberFormat(undefined, {
    style: 'currency',
    currency,
    notation,
    maximumFractionDigits,
    minimumFractionDigits,
  }).format(amount);
}

/**
 * Calculate discount percentage between original price and sale price
 * @param originalPrice - The original price
 * @param salePrice - The sale price
 * @returns Discount percentage as a whole number
 */
export function calculateDiscountPercentage(
  originalPrice: number,
  salePrice: number
): number {
  if (!originalPrice || !salePrice || originalPrice <= 0) return 0;
  return Math.round(((originalPrice - salePrice) / originalPrice) * 100);
}

/**
 * Format a date string
 * @param dateString - The date string to format
 * @param options - Date formatting options
 * @returns Formatted date string
 */
export function formatDate(dateString: string, options: Intl.DateTimeFormatOptions = {}): string {
  const date = new Date(dateString);

  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };

  return date.toLocaleDateString(undefined, { ...defaultOptions, ...options });
}

/**
 * Format a date with time
 * @param dateString - The date string to format
 * @returns Formatted date and time string
 */
export function formatDateTime(dateString: string): string {
  const date = new Date(dateString);

  return date.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Format a number with commas
 * @param num - The number to format
 * @returns Formatted number string with commas
 */
export function formatNumber(num: number): string {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * Truncate text with ellipsis
 * @param text - The text to truncate
 * @param maxLength - Maximum length before truncation
 * @returns Truncated text with ellipsis if needed
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

/**
 * Format file size
 * @param bytes - The file size in bytes
 * @returns Formatted file size string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format a percentage
 * @param value - The percentage value
 * @param decimals - Number of decimal places
 * @returns Formatted percentage string
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * Format a phone number
 * @param phoneNumber - The phone number to format
 * @returns Formatted phone number string
 */
export function formatPhoneNumber(phoneNumber: string): string {
  // This is a simple formatter for international numbers
  // You may want to use a library like libphonenumber-js for more robust formatting

  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');

  // Check if it's a valid number with at least 10 digits
  if (cleaned.length < 10) return phoneNumber;

  // Format as international number
  if (cleaned.startsWith('1') && cleaned.length === 11) {
    // US number
    return `+${cleaned.slice(0, 1)} (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  } else {
    // Generic international format
    return `+${cleaned}`;
  }
}
