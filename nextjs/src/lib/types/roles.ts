// Role-based access control types and utilities

export type UserRole = 'user' | 'admin';

export interface RolePermissions {
  canAccessAdmin: boolean;
  canManageStores: boolean;
  canManageOrders: boolean;
  canManageUsers: boolean;
  canManagePayouts: boolean;
  canViewAnalytics: boolean;
  canManageFeatureToggles: boolean;
  canUploadCSV: boolean;
}

export const ROLE_PERMISSIONS: Record<UserRole, RolePermissions> = {
  user: {
    canAccessAdmin: false,
    canManageStores: false,
    canManageOrders: false,
    canManageUsers: false,
    canManagePayouts: false,
    canViewAnalytics: false,
    canManageFeatureToggles: false,
    canUploadCSV: false,
  },
  admin: {
    canAccessAdmin: true,
    canManageStores: true,
    canManageOrders: true,
    canManageUsers: true,
    canManagePayouts: true,
    canViewAnalytics: true,
    canManageFeatureToggles: true,
    canUploadCSV: true,
  },
};

export interface UserWithRole {
  id: string;
  email: string;
  role: UserRole;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
}

// Removed StoreOwnerProfile - no longer needed

export interface OrderStoreItem {
  id: string;
  order_id: string;
  store_id: string;
  total_amount: number;
  currency: string;
  status: 'pending' | 'under_review' | 'awaiting_store_confirmation' | 'accepted' | 'rejected' | 'completed';
  rejection_reason?: string;
  rejection_notes?: string;
  accepted_at?: string;
  rejected_at?: string;
  created_at: string;
  updated_at: string;
  store?: {
    id: string;
    name: string;
    slug: string;
  };
  order?: {
    id: string;
    user_id: string;
    total: number;
    currency: string;
    status: string;
    created_at: string;
    shipping_name?: string;
    shipping_email?: string;
    shipping_phone?: string;
  };
}

export interface NotificationData {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  notification_category: 'general' | 'order' | 'payment' | 'store';
  read: boolean;
  data?: any;
  created_at: string;
  updated_at: string;
}

// Feature toggle types
export interface FeatureToggle {
  id: string;
  feature_key: string;
  feature_name: string;
  description?: string;
  enabled: boolean;
  role_restrictions?: UserRole[];
  created_at: string;
  updated_at: string;
}

export type FeatureKey =
  | 'store_management'
  | 'product_management'
  | 'order_management'
  | 'payout_management'
  | 'analytics_view'
  | 'user_management'
  | 'csv_upload'
  | 'finder_earnings'
  | 'settings_access';

// Utility functions
export function hasPermission(role: UserRole, permission: keyof RolePermissions): boolean {
  return ROLE_PERMISSIONS[role][permission];
}

export function canAccessRoute(role: UserRole, route: string): boolean {
  if (route.startsWith('/admin')) {
    return hasPermission(role, 'canAccessAdmin');
  }
  return true; // Allow access to public routes
}

export function getRedirectPath(role: UserRole): string {
  switch (role) {
    case 'admin':
      return '/admin';
    case 'user':
    default:
      return '/';
  }
}

export function getRoleDisplayName(role: UserRole): string {
  switch (role) {
    case 'admin':
      return 'Administrator';
    case 'user':
    default:
      return 'User';
  }
}

export function getRoleBadgeColor(role: UserRole): string {
  switch (role) {
    case 'admin':
      return 'bg-red-100 text-red-800';
    case 'user':
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

// Order rejection reasons
export const ORDER_REJECTION_REASONS = [
  'out_of_stock',
  'invalid_address',
  'payment_issue',
  'store_closed',
  'product_discontinued',
  'other'
] as const;

export type OrderRejectionReason = typeof ORDER_REJECTION_REASONS[number];

export function getRejectionReasonLabel(reason: OrderRejectionReason): string {
  switch (reason) {
    case 'out_of_stock':
      return 'Out of Stock';
    case 'invalid_address':
      return 'Invalid Delivery Address';
    case 'payment_issue':
      return 'Payment Issue';
    case 'store_closed':
      return 'Store Temporarily Closed';
    case 'product_discontinued':
      return 'Product Discontinued';
    case 'other':
      return 'Other Reason';
    default:
      return 'Unknown Reason';
  }
}
