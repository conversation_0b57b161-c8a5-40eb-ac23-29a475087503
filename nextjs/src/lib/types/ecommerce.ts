export interface Category {
  id: number | string;
  created_at?: string;
  createdAt?: string;
  name: string;
  slug: string;
  description?: string | null;
  image_url?: string | null;
  imageUrl?: string | null;
}

export interface Product {
  id: number | string;
  created_at?: string;
  createdAt?: string;
  updatedAt?: string;
  name: string;
  slug?: string;
  description: string | null;
  price: number;
  compare_at_price?: number | null;
  salePrice?: number | null;
  category_id?: number | null;
  categoryId?: string | null;
  storeId?: string | null;
  featured: boolean;
  inventory_quantity?: number;
  inStock?: boolean;
  is_local?: boolean; // Indicates whether the product is local or international
  rating?: number | null;
  image_url?: string | null;
  imageUrl?: string | null;
  category?: Category;
  images?: ProductImage[];
}

export interface ProductImage {
  id: number;
  created_at: string;
  product_id: number;
  url: string;
  alt: string | null;
  position: number;
}

export interface Order {
  id: number;
  created_at: string;
  user_id: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  total: number;
  shipping_address: Address | null;
  billing_address: Address | null;
  payment_intent_id: string | null;
  shipping_method: string | null;
  tracking_number: string | null;
  items?: OrderItem[];
}

export interface OrderItem {
  id: number;
  created_at: string;
  order_id: number;
  product_id: number;
  quantity: number;
  price: number;
  name: string;
  options: Record<string, any> | null;
  product?: Product;
}

export interface CartItem {
  id: string;
  created_at: string;
  updated_at?: string;
  user_id: string;
  product_id: string;
  quantity: number;
  options: Record<string, any> | null;
  product?: Product;
}

export interface Review {
  id: number;
  created_at: string;
  user_id: string;
  product_id: number;
  rating: number;
  title: string | null;
  content: string | null;
  status: 'pending' | 'approved' | 'rejected';
  user?: {
    email: string;
  };
}

export interface WishlistItem {
  id: string;
  created_at: string;
  user_id: string;
  product_id: string;
  product?: Product;
}

export interface Address {
  first_name: string;
  last_name: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  phone?: string;
}
