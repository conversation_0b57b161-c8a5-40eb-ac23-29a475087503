import { createSSRClient } from '@/lib/supabase/server';
import { createSPASassClient } from '@/lib/supabase/client';
import { formatCurrency } from '@/utils';

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  content: string;
  type: string;
  read: boolean;
  data?: Record<string, any>;
  created_at: string;
}

export interface CreateNotificationParams {
  user_id: string;
  title: string;
  content: string;
  type: string;
  data?: Record<string, any>;
}

export class NotificationService {
  // Create a notification for a specific user
  static async createNotification(params: CreateNotificationParams): Promise<Notification | null> {
    try {
      const supabase = await createSSRClient();
      const { data, error } = await supabase
        .from('notifications')
        .insert({
          user_id: params.user_id,
          title: params.title,
          content: params.content,
          type: params.type,
          data: params.data || {},
          read: false
        })
        .select()
        .single();

      if (error) throw error;
      return data as Notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      return null;
    }
  }

  // Create a notification for all admin users
  static async createAdminNotification(params: Omit<CreateNotificationParams, 'user_id'>): Promise<void> {
    try {
      const supabase = await createSSRClient();

      // Get all admin users - check if role column exists first
      const { data: admins, error: adminsError } = await supabase
        .from('profiles')
        .select('id, role')
        .eq('role', 'admin');

      if (adminsError) {
        // If role column doesn't exist, fall back to hardcoded admin email
        console.warn('Role column not found, using fallback admin email');
        const { data: fallbackAdmin, error: fallbackError } = await supabase
          .from('profiles')
          .select('id')
          .eq('email', '<EMAIL>')
          .single();

        if (fallbackError || !fallbackAdmin) {
          console.warn('No admin users found to notify');
          return;
        }

        // Create notification for fallback admin
        const { error } = await supabase
          .from('notifications')
          .insert({
            user_id: fallbackAdmin.id,
            title: params.title,
            content: params.content,
            type: params.type,
            data: params.data || {},
            read: false
          });

        if (error) throw error;
        return;
      }

      if (!admins || admins.length === 0) {
        console.warn('No admin users found to notify');
        return;
      }

      // Create notifications for each admin
      const notifications = admins.map(admin => ({
        user_id: admin.id,
        title: params.title,
        content: params.content,
        type: params.type,
        data: params.data || {},
        read: false
      }));

      const { error } = await supabase
        .from('notifications')
        .insert(notifications);

      if (error) throw error;
    } catch (error) {
      console.error('Error creating admin notifications:', error);
    }
  }

  // Create a payment notification for admins
  static async createPaymentNotification(orderId: string, amount: number, currency: string): Promise<void> {
    return this.createAdminNotification({
      title: 'New Payment Received',
      content: `A new payment of ${formatCurrency(amount, currency)} has been received for order #${orderId.substring(0, 8)}`,
      type: 'payment',
      data: { orderId, amount, currency }
    });
  }

  // Create an order status update notification for admins
  static async createOrderStatusNotification(orderId: string, status: string): Promise<void> {
    const statusLabels: Record<string, string> = {
      'pending': 'Pending',
      'under_review': 'Under Review',
      'awaiting_store_confirmation': 'Awaiting Store Confirmation',
      'accepted_by_store': 'Accepted by Store',
      'processing': 'Processing',
      'shipped': 'Shipped',
      'delivered': 'Delivered',
      'cancelled': 'Cancelled',
      'refunded': 'Refunded'
    };

    const statusLabel = statusLabels[status] || status;

    return this.createAdminNotification({
      title: 'Order Status Updated',
      content: `Order #${orderId.substring(0, 8)} has been updated to ${statusLabel}`,
      type: 'order',
      data: { orderId, status }
    });
  }

  // Create a notification for store owners
  static async createStoreOwnerNotification(storeId: string, params: Omit<CreateNotificationParams, 'user_id'>): Promise<void> {
    try {
      const supabase = await createSSRClient();

      // Get the store owner
      const { data: store, error: storeError } = await supabase
        .from('stores')
        .select('owner_id')
        .eq('id', storeId)
        .single();

      if (storeError) throw storeError;

      if (!store || !store.owner_id) {
        console.warn('No store owner found for store:', storeId);
        return;
      }

      // Create notification for the store owner
      const { error } = await supabase
        .from('notifications')
        .insert({
          user_id: store.owner_id,
          title: params.title,
          content: params.content,
          type: params.type,
          data: params.data || {},
          read: false
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error creating store owner notification:', error);
    }
  }

  // Create order review notification for store owners
  static async createStoreOrderReviewNotification(orderId: string, storeId: string): Promise<void> {
    return this.createStoreOwnerNotification(storeId, {
      title: 'New Order for Review',
      content: `A new order #${orderId.substring(0, 8)} requires your confirmation. Please review and confirm availability.`,
      type: 'order_review',
      data: { orderId, storeId }
    });
  }

  // Create store order confirmation notification for admins
  static async createStoreOrderConfirmationNotification(orderId: string, storeId: string, storeName: string): Promise<void> {
    return this.createAdminNotification({
      title: 'Store Confirmed Order',
      content: `${storeName} has confirmed order #${orderId.substring(0, 8)} and it's ready for processing.`,
      type: 'store_confirmation',
      data: { orderId, storeId, storeName }
    });
  }

  // Create payment confirmation notification for store owners
  static async createPaymentConfirmationNotification(orderId: string, storeId: string, amount: number, currency: string): Promise<void> {
    return this.createStoreOwnerNotification(storeId, {
      title: 'Payment Confirmed',
      content: `Payment of ${currency} ${amount} for order #${orderId.substring(0, 8)} has been confirmed. Order is being processed.`,
      type: 'payment_confirmed',
      data: { orderId, storeId, amount, currency }
    });
  }

  // Create an order status update notification for a customer
  static async createCustomerOrderNotification(userId: string, orderId: string, status: string): Promise<void> {
    const statusLabels: Record<string, string> = {
      'pending': 'Pending',
      'under_review': 'Under Review',
      'awaiting_store_confirmation': 'Pending Store Confirmation',
      'accepted_by_store': 'Confirmed by Store',
      'processing': 'Processing',
      'shipped': 'Shipped',
      'delivered': 'Delivered',
      'cancelled': 'Cancelled',
      'refunded': 'Refunded'
    };

    const statusMessages: Record<string, string> = {
      'pending': 'Your order is pending confirmation',
      'under_review': 'Your order is being reviewed by our team',
      'awaiting_store_confirmation': 'Your order is pending confirmation from the store',
      'accepted_by_store': 'Your order has been confirmed by the store and is being processed',
      'processing': 'Your order is being processed',
      'shipped': 'Your order has been shipped',
      'delivered': 'Your order has been delivered',
      'cancelled': 'Your order has been cancelled',
      'refunded': 'Your order has been refunded'
    };

    const statusLabel = statusLabels[status] || status;
    const message = statusMessages[status] || `Your order status has been updated to ${statusLabel}`;

    return this.createNotification({
      user_id: userId,
      title: `Order Status: ${statusLabel}`,
      content: message,
      type: 'order',
      data: { orderId, status }
    }).then(() => {});
  }

  // Get notifications for a user
  static async getUserNotifications(userId: string, limit = 10, offset = 0): Promise<{ notifications: Notification[], count: number }> {
    try {
      const supabase = await createSSRClient();

      // Get count
      const { count, error: countError } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (countError) throw countError;

      // Get notifications
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw error;

      return {
        notifications: data as Notification[],
        count: count || 0
      };
    } catch (error) {
      console.error('Error getting user notifications:', error);
      return { notifications: [], count: 0 };
    }
  }

  // Mark a notification as read
  static async markAsRead(notificationId: string): Promise<void> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);

      if (error) throw error;
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  // Mark all notifications as read for a user
  static async markAllAsRead(userId: string): Promise<void> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', userId)
        .eq('read', false);

      if (error) throw error;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }
}
