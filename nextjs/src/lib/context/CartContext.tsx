'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { createSPASassClient } from '@/lib/supabase/client';
import { EcommerceClientService } from '@/lib/services/ecommerce-client';
import { CartItem } from '@/lib/types/ecommerce';

interface CartContextType {
  cartItems: CartItem[];
  cartCount: number;
  loading: boolean;
  refreshCart: () => Promise<void>;
  addToCart: (productId: string | number, quantity: number) => Promise<void>;
  updateCartItem: (cartItemId: string, quantity: number) => Promise<void>;
  removeFromCart: (cartItemId: string) => Promise<void>;
  clearCart: () => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [userId, setUserId] = useState<string | null>(null);

  const fetchCart = async () => {
    try {
      setLoading(true);
      const supabase = await createSPASassClient();
      const { data: { user } } = await supabase.getSupabaseClient().auth.getUser();

      if (user) {
        // User is logged in
        const currentUserId = user.id;
        
        // If this is a new user session, clear any guest cart
        if (userId !== currentUserId) {
          localStorage.removeItem('guest_cart');
        }
        
        setUserId(currentUserId);
        
        try {
          const items = await EcommerceClientService.getCart(currentUserId);
          setCartItems(items);
        } catch (error) {
          console.error('Error fetching cart items:', error);
          setCartItems([]);
        }
      } else {
        // User is not logged in
        setUserId(null);
        
        // Load guest cart from localStorage
        const localCart = JSON.parse(localStorage.getItem('guest_cart') || '[]');
        if (localCart.length > 0) {
          // Fetch product data for guest cart items
          const productIds = localCart.map((item: any) => item.productId);
          
          try {
            const { data: products, error: productsError } = await supabase.getSupabaseClient()
              .from('products')
              .select(`
                *,
                store:stores(*),
                category:categories(*),
                images:product_images(*)
              `)
              .in('id', productIds);

            if (productsError) {
              console.error('Error fetching guest cart products:', productsError);
            }

            const guestItems = localCart.map((item: any) => {
              const product = products?.find(p => p.id === item.productId);
              return {
                id: `guest_${item.productId}`,
                user_id: 'guest',
                product_id: item.productId,
                quantity: item.quantity,
                options: null,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                product: product ? {
                  id: product.id,
                  name: product.name,
                  slug: product.slug,
                  description: product.description,
                  price: product.price,
                  compare_at_price: product.compare_at_price,
                  currency: product.currency,
                  featured: product.featured,
                  inStock: product.in_stock || true,
                  is_local: product.is_local ?? true, // Include is_local field for checkout logic
                  rating: product.rating,
                  images: product.images || [],
                  store: product.store,
                  category: product.category
                } : null
              };
            });
            setCartItems(guestItems);
          } catch (error) {
            console.error('Error fetching guest cart product data:', error);
            // Fallback to items without product data
            const guestItems = localCart.map((item: any) => ({
              id: `guest_${item.productId}`,
              user_id: 'guest',
              product_id: item.productId,
              quantity: item.quantity,
              options: null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              product: null
            }));
            setCartItems(guestItems);
          }
        } else {
          setCartItems([]);
        }
      }
    } catch (error) {
      console.error('Error fetching cart:', error);
      setCartItems([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCart();
  }, []);

  const refreshCart = async () => {
    await fetchCart();
  };

  const addToCart = async (productId: string | number, quantity: number) => {
    try {
      const supabase = await createSPASassClient();
      const { data: { user } } = await supabase.getSupabaseClient().auth.getUser();

      if (!user) {
        // First verify that the product exists
        const { data: product, error: productError } = await supabase.getSupabaseClient()
          .from('products')
          .select('id')
          .eq('id', productId)
          .single();

        if (productError || !product) {
          throw new Error('Product not found');
        }

        // Store in localStorage for unauthenticated users
        const localCart = JSON.parse(localStorage.getItem('guest_cart') || '[]');
        const existingItemIndex = localCart.findIndex((item: any) => item.productId === productId);

        if (existingItemIndex >= 0) {
          localCart[existingItemIndex].quantity += quantity;
        } else {
          localCart.push({ productId, quantity, addedAt: Date.now() });
        }

        localStorage.setItem('guest_cart', JSON.stringify(localCart));

        // Update local state to reflect the change
        setCartItems(prev => {
          const existingIndex = prev.findIndex(item => item.product_id === productId);
          if (existingIndex >= 0) {
            return prev.map((item, index) =>
              index === existingIndex
                ? { ...item, quantity: item.quantity + quantity }
                : item
            );
          } else {
            return [...prev, {
              id: `guest_${productId}`,
              user_id: 'guest',
              product_id: productId,
              quantity,
              options: null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              product: null // Will be resolved when they sign in
            } as any];
          }
        });
        return;
      }

      await EcommerceClientService.addToCart(productId, quantity);
      await refreshCart();
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    }
  };

  const updateCartItem = async (cartItemId: string, quantity: number) => {
    try {
      const supabase = await createSPASassClient();
      const { data: { user } } = await supabase.getSupabaseClient().auth.getUser();

      if (!user) {
        // Handle guest cart update
        if (cartItemId.startsWith('guest_')) {
          const productId = cartItemId.replace('guest_', '');
          const localCart = JSON.parse(localStorage.getItem('guest_cart') || '[]');
          const updatedCart = localCart.map((item: any) =>
            item.productId === productId ? { ...item, quantity } : item
          );
          localStorage.setItem('guest_cart', JSON.stringify(updatedCart));
          await refreshCart();
          return;
        }
      }

      await EcommerceClientService.updateCartItem(cartItemId, quantity);

      // Update local state immediately for better UX
      setCartItems(prevItems =>
        prevItems.map(item =>
          item.id === cartItemId ? { ...item, quantity } : item
        )
      );

      // Then refresh from server to ensure consistency
      await refreshCart();
    } catch (error) {
      console.error('Error updating cart item:', error);
      throw error;
    }
  };

  const removeFromCart = async (cartItemId: string) => {
    try {
      const supabase = await createSPASassClient();
      const { data: { user } } = await supabase.getSupabaseClient().auth.getUser();

      if (!user) {
        // Handle guest cart removal
        if (cartItemId.startsWith('guest_')) {
          const productId = cartItemId.replace('guest_', '');
          const localCart = JSON.parse(localStorage.getItem('guest_cart') || '[]');
          const updatedCart = localCart.filter((item: any) => item.productId !== productId);
          localStorage.setItem('guest_cart', JSON.stringify(updatedCart));
          await refreshCart();
          return;
        }
      }

      await EcommerceClientService.removeFromCart(cartItemId);

      // Update local state immediately for better UX
      setCartItems(prevItems => prevItems.filter(item => item.id !== cartItemId));

      // Then refresh from server to ensure consistency
      await refreshCart();
    } catch (error) {
      console.error('Error removing from cart:', error);
      throw error;
    }
  };

  const clearCart = async () => {
    try {
      const supabase = await createSPASassClient();
      const { data: { user } } = await supabase.getSupabaseClient().auth.getUser();

      if (user) {
        // Clear database cart for logged-in user
        const { error } = await supabase.getSupabaseClient()
          .from('cart')
          .delete()
          .eq('user_id', user.id);
        
        if (error) {
          console.error('Error clearing cart from database:', error);
        }
      }

      // Clear guest cart from localStorage
      localStorage.removeItem('guest_cart');
      
      // Update local state
      setCartItems([]);
    } catch (error) {
      console.error('Error clearing cart:', error);
      throw error;
    }
  };

  return (
    <CartContext.Provider
      value={{
        cartItems,
        cartCount: cartItems.length,
        loading,
        refreshCart,
        addToCart,
        updateCartItem,
        removeFromCart,
        clearCart,
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
